```markdown
# 1. Authentication & User Management Roadmap

Fully use Clean Architecture and DDD

avoid use prisma in domain layer

Tech stack:
- Typescript
- Node.js
- Vite
- Vitest
- Tailwind CSS

## 1.1 TaaS
Tenant as a Service

- [ ] 1.1.1. Create Tenant (or user pool) - M1
- [ ] 1.1.2. Delete Tenant (or user pool) - M2
- [ ] 1.1.3. Get Tenant (or user pool) - M3
- [ ] 1.1.4. Update Tenant (or user pool) - M4
- [ ] 1.1.5. Get Tenants (or user pools) - M5
- [ ] 1.1.6. Update Tenants (or user pools) - M6
- [ ] 1.1.7. Delete Tenants (or user pools) - M7
- [ ] 1.1.8. Tenant is a User Pool - M8
- [ ] 1.1.9. Tenant have a list of roles - M9
- [ ] 1.1.10. Role is a User Group - M10
- [ ] 1.1.11. Role have a list of policies - M11
- [ ] 1.1.12. Policy is a User Policy - M12
- [ ] 1.1.13. Policy have a list of actions - M13
- [ ] 1.1.14. Action is a User Action - M14
- [ ] 1.1.15. Action have a list of resources - M15
- [ ] 1.1.16. Resource is a User Resourc...

## 2. CDC Pipeline Migration - Knowledge Base

### 2.1 Migration Overview
**Date**: 2025-07-03  
**Migration Type**: Debezium → Enhanced Debezium (Simplified Architecture)  
**Reason**: Estuary Flow is cloud-based and doesn't fit our self-hosted Docker infrastructure 

### 2.2 Current State Documentation

#### 2.2.1 Pre-Migration CDC Architecture
```
PostgreSQL → Debezium → Kafka → NATS Connector → NATS JetStream
```

#### 2.2.2 Active Services (Before Migration)
- `debezium-connect`: Running on port 8083
- `kafka`: Running on port 9092  
- `zookeeper`: Supporting Kafka cluster
- `nats-connector`: Custom bridge service
- `debezium-ui`: Management interface
- `debezium-connector-setup`: Configuration service

#### 2.2.3 Data Backup Status
✅ **Appointment Outbox Data**: Backed up to `/tmp/appointment_outbox_backup_20250704_000056.sql`
- **Total Events**: 10 AppointmentCreated events
- **Source**: appointmentOutbox
- **Backup Method**: pg_dump with --data-only --inserts

✅ **Debezium Connector Config**: Backed up to `/tmp/debezium_connector_config_backup_*.json`
- **Connector Name**: beauty-crm-appointment-outbox-connector
- **Status**: RUNNING before migration

#### 2.2.4 Current Connector Configuration
- **Database**: beauty_crm_appointment
- **Table**: appointment_outbox
- **Replication Slot**: flow_slot
- **Publication**: flow_publication
- **Kafka Topic**: appointment.events
- **NATS Stream**: APPOINTMENT_EVENTS
- **Subject Pattern**: appointment.events.{eventType}

### 2.3 Proposed Enhanced Architecture

#### 2.3.1 New Simplified Pipeline
```
PostgreSQL → Debezium → HTTP Sink → NATS Bridge → NATS JetStream
```

#### 2.3.2 Key Improvements
1. **Eliminate Kafka Dependency**: Direct HTTP-based communication
2. **Simplified Resource Management**: Fewer moving parts
3. **Enhanced Reliability**: Better error handling and recovery
4. **Improved Monitoring**: Comprehensive health checks
5. **Resource Optimization**: Lower memory and CPU usage

#### 2.3.3 Benefits Over Estuary Flow
- ✅ **Self-Hosted**: Maintains Docker infrastruc...
```