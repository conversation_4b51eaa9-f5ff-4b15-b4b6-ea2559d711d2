#!/bin/bash

# Quick Start SDLC Monster
# Simple script to start the autonomous development pipeline

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🚀 Quick Start SDLC Monster${NC}"
echo "================================"

# Check if we're in the right directory
if [[ ! -f "docker-compose.yml" ]]; then
    echo -e "${RED}❌ docker-compose.yml not found${NC}"
    echo "Please run from autonomous-ai-agents directory"
    exit 1
fi

# Set Gemini API key if not set
if [[ -z "$GEMINI_API_KEY" ]]; then
    echo -e "${YELLOW}⚠️  GEMINI_API_KEY not set${NC}"
    echo "Please export your Gemini API key:"
    echo "export GEMINI_API_KEY=your_api_key_here"
    echo ""
    echo "Or create .env file with:"
    echo "GEMINI_API_KEY=your_api_key_here"
    exit 1
fi

echo -e "${GREEN}✅ Gemini API key found${NC}"

# Create directories
mkdir -p data/n8n workflows credentials nodes

# Start services
echo -e "${BLUE}🔄 Starting services...${NC}"
docker compose down --remove-orphans 2>/dev/null || true
docker compose up -d

# Wait for n8n
echo -e "${YELLOW}⏳ Waiting for n8n...${NC}"
for i in {1..30}; do
    if curl -s http://localhost:5678/healthz > /dev/null 2>&1; then
        echo -e "${GREEN}✅ n8n is ready!${NC}"
        break
    fi
    if [[ $i -eq 30 ]]; then
        echo -e "${RED}❌ n8n failed to start${NC}"
        exit 1
    fi
    sleep 2
    echo -n "."
done

echo ""
echo -e "${GREEN}🎉 SDLC Monster is running!${NC}"
echo ""
echo -e "${BLUE}📊 Access Points:${NC}"
echo "  🔧 n8n Editor: http://localhost:5678"
echo "  📋 Executions: http://localhost:5678/executions"
echo ""
echo -e "${BLUE}🔍 Next Steps:${NC}"
echo "  1. Open http://localhost:5678 in your browser"
echo "  2. Create workflows to process tasks"
echo "  3. Use Gemini AI for task analysis"
echo "  4. Make real file changes in /workspace"
echo ""
echo -e "${YELLOW}💡 The workspace is mounted at /workspace inside n8n${NC}"
echo -e "${YELLOW}💡 You can make real file changes that show up in git status${NC}"
echo ""
echo -e "${GREEN}✨ Ready to build autonomous workflows!${NC}"
