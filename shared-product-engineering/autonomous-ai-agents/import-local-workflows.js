#!/usr/bin/env node

/**
 * Local SDLC Workflow Importer
 * Imports local file-based SDLC agents into n8n
 */

const fs = require('fs');
const path = require('path');
const http = require('http');

// Configuration
const N8N_BASE_URL = 'http://localhost:5678';
const WORKFLOWS_DIR = path.join(__dirname, 'workflows');
const MAX_RETRIES = 10;
const RETRY_DELAY = 3000;

// Colors
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  purple: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(url, options = {}) {\n  return new Promise((resolve, reject) => {\n    const req = http.request(url, options, (res) => {\n      let data = '';\n      res.on('data', chunk => data += chunk);\n      res.on('end', () => {\n        try {\n          const jsonData = data ? JSON.parse(data) : {};\n          resolve({ statusCode: res.statusCode, data: jsonData });\n        } catch (error) {\n          resolve({ statusCode: res.statusCode, data: data });\n        }\n      });\n    });\n    \n    req.on('error', reject);\n    \n    if (options.body) {\n      req.write(options.body);\n    }\n    \n    req.end();\n  });\n}\n\nasync function waitForN8n() {\n  log('⏳ Waiting for n8n to be ready...', 'yellow');\n  \n  for (let i = 0; i < MAX_RETRIES; i++) {\n    try {\n      const response = await makeRequest(`${N8N_BASE_URL}/healthz`);\n      if (response.statusCode === 200) {\n        log('✅ n8n is ready!', 'green');\n        return true;\n      }\n    } catch (error) {\n      // n8n not ready yet\n    }\n    \n    if (i < MAX_RETRIES - 1) {\n      log(`   Attempt ${i + 1}/${MAX_RETRIES} failed, retrying in ${RETRY_DELAY/1000}s...`, 'yellow');\n      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));\n    }\n  }\n  \n  throw new Error('n8n failed to become ready');\n}\n\nasync function getExistingWorkflows() {\n  try {\n    const response = await makeRequest(`${N8N_BASE_URL}/api/v1/workflows`);\n    if (response.statusCode === 200) {\n      return response.data.data || [];\n    }\n    return [];\n  } catch (error) {\n    log(`⚠️ Could not fetch existing workflows: ${error.message}`, 'yellow');\n    return [];\n  }\n}\n\nasync function importWorkflow(workflowFile) {\n  const workflowPath = path.join(WORKFLOWS_DIR, workflowFile);\n  \n  try {\n    const workflowData = JSON.parse(fs.readFileSync(workflowPath, 'utf8'));\n    const workflowName = workflowData.name;\n    \n    log(`📥 Importing: ${workflowName}`, 'blue');\n    \n    // Check if workflow already exists\n    const existingWorkflows = await getExistingWorkflows();\n    const existingWorkflow = existingWorkflows.find(w => w.name === workflowName);\n    \n    if (existingWorkflow) {\n      log(`   🔄 Updating existing workflow (ID: ${existingWorkflow.id})`, 'yellow');\n      \n      const updateResponse = await makeRequest(`${N8N_BASE_URL}/api/v1/workflows/${existingWorkflow.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(workflowData)\n      });\n      \n      if (updateResponse.statusCode === 200) {\n        log(`   ✅ Updated: ${workflowName}`, 'green');\n        return { action: 'updated', workflow: updateResponse.data };\n      } else {\n        log(`   ❌ Update failed: ${updateResponse.statusCode}`, 'red');\n        return { action: 'failed', error: updateResponse.data };\n      }\n    } else {\n      const createResponse = await makeRequest(`${N8N_BASE_URL}/api/v1/workflows`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(workflowData)\n      });\n      \n      if (createResponse.statusCode === 201 || createResponse.statusCode === 200) {\n        log(`   ✅ Created: ${workflowName} (ID: ${createResponse.data.id})`, 'green');\n        return { action: 'created', workflow: createResponse.data };\n      } else {\n        log(`   ❌ Creation failed: ${createResponse.statusCode}`, 'red');\n        return { action: 'failed', error: createResponse.data };\n      }\n    }\n  } catch (error) {\n    log(`   ❌ Error importing ${workflowFile}: ${error.message}`, 'red');\n    return { action: 'failed', error: error.message };\n  }\n}\n\nasync function activateWorkflow(workflowId, workflowName) {\n  try {\n    const response = await makeRequest(`${N8N_BASE_URL}/api/v1/workflows/${workflowId}/activate`, {\n      method: 'POST'\n    });\n    \n    if (response.statusCode === 200) {\n      log(`   ⚡ Activated: ${workflowName}`, 'green');\n      return true;\n    } else {\n      log(`   ⚠️ Activation failed: ${workflowName}`, 'yellow');\n      return false;\n    }\n  } catch (error) {\n    log(`   ⚠️ Activation error: ${workflowName}`, 'yellow');\n    return false;\n  }\n}\n\nasync function main() {\n  log('🤖 Local SDLC Workflow Importer', 'purple');\n  log('=================================', 'purple');\n  \n  try {\n    await waitForN8n();\n    \n    if (!fs.existsSync(WORKFLOWS_DIR)) {\n      log(`❌ Workflows directory not found: ${WORKFLOWS_DIR}`, 'red');\n      process.exit(1);\n    }\n    \n    // Get local workflow files (prioritize local-* files)\n    const allFiles = fs.readdirSync(WORKFLOWS_DIR).filter(file => file.endsWith('.json'));\n    const localFiles = allFiles.filter(file => file.startsWith('local-')).sort();\n    const otherFiles = allFiles.filter(file => !file.startsWith('local-')).sort();\n    const workflowFiles = [...localFiles, ...otherFiles];\n    \n    if (workflowFiles.length === 0) {\n      log('⚠️ No workflow files found', 'yellow');\n      process.exit(0);\n    }\n    \n    log(`📁 Found ${workflowFiles.length} workflow files (${localFiles.length} local agents)`, 'blue');\n    \n    const results = {\n      created: 0,\n      updated: 0,\n      failed: 0,\n      activated: 0\n    };\n    \n    for (const workflowFile of workflowFiles) {\n      const result = await importWorkflow(workflowFile);\n      \n      if (result.action === 'created') {\n        results.created++;\n        if (await activateWorkflow(result.workflow.id, result.workflow.name)) {\n          results.activated++;\n        }\n      } else if (result.action === 'updated') {\n        results.updated++;\n        if (await activateWorkflow(result.workflow.id, result.workflow.name)) {\n          results.activated++;\n        }\n      } else {\n        results.failed++;\n      }\n      \n      await new Promise(resolve => setTimeout(resolve, 1000));\n    }\n    \n    log('', 'reset');\n    log('🎉 Local SDLC Workflow Import Complete!', 'green');\n    log('========================================', 'green');\n    log(`📊 Results:`, 'blue');\n    log(`   ✅ Created: ${results.created}`, 'green');\n    log(`   🔄 Updated: ${results.updated}`, 'yellow');\n    log(`   ❌ Failed: ${results.failed}`, 'red');\n    log(`   ⚡ Activated: ${results.activated}`, 'green');\n    log('', 'reset');\n    \n    if (results.activated > 0) {\n      log('🚀 Local SDLC Agents are now running!', 'purple');\n      log('📊 Monitor at: http://localhost:5678/executions', 'cyan');\n      log('🔧 Edit workflows: http://localhost:5678/workflows', 'cyan');\n      log('', 'reset');\n      log('💡 The agents will:', 'blue');\n      log('   1. 🔍 Scan local files every 5 minutes for tasks', 'cyan');\n      log('   2. 🧠 Analyze tasks with Gemini AI', 'cyan');\n      log('   3. 📝 Make REAL file changes in /workspace', 'cyan');\n      log('   4. ✅ Run TypeScript/ESLint quality checks', 'cyan');\n      log('   5. 📊 Create actual git commits', 'cyan');\n    }\n    \n    process.exit(results.failed > 0 ? 1 : 0);\n    \n  } catch (error) {\n    log(`❌ Fatal error: ${error.message}`, 'red');\n    process.exit(1);\n  }\n}\n\nif (require.main === module) {\n  main();\n}\n\nmodule.exports = { importWorkflow, waitForN8n };"
      },
      "id": "import-script",
      "name": "Import Local Workflows",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [100, 300]
    }
  ],
  "connections": {},
  "active": false,
  "settings": {},
  "versionId": "1",
  "meta": {
    "templateCredsSetupCompleted": true
  },
  "id": "import-local-workflows"
}
