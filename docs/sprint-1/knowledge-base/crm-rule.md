```markdown
# 1. Authentication & User Management Roadmap

Fully use Clean Architecture and DDD

avoid use prisma in domain layer

Tech stack:
- Typescript
- Node.js
- Hono
- Neon
- Vitest
- TypeScript
- Vite
- Vitest
- Tailwind CSS
- Biome

## 1.1 TaaS
Tenant as a Service

- [ ] 1.1.1. Create Tenant (or user pool) - M1
- [ ] 1.1.2. Delete Tenant (or user pool) - M2
- [ ] 1.1.3. Get Tenant (or user pool) - M3
- [ ] 1.1.4. Update Tenant (or user pool) - M4
- [ ] 1.1.5. Get Tenants (or user pools) - M5
- [ ] 1.1.6. Update Tenants (or user pools) - M6
- [ ] 1.1.7. Delete Tenants (or user pools) - M7
- [ ] 1.1.8. Tenant is a User Pool - M8
- [ ] 1.1.9. Tenant have a list of roles - M9
- [ ] 1.1.10. Role is a User Group - M10
- [ ] 1.1.11. Role have a list of policies - M11
- [ ] 1.1.12. Policy is a User Policy - M12
- [ ] 1.1.13. Policy have a list of actions - M13
- [ ] 1.1.14. Action is a User Action - M14
- [ ] 1.1.15. Action have a list of resources - M15
- [ ] 1.1.16. Resource is a User Resourc...

## 1.2 Backend Architecture

Hono provides a fast, lightweight framework for building serverless applications.
Node.js: JavaScript runtime for server-side development.
Neon: Serverless PostgreSQL database.
Prisma: Modern ORM for database access and management.
Vitest: Fast unit testing framework for Vite projects.
TypeScript: Typed superset of JavaScript for enhanced developer experience.
Biome: Toolchain for web projects, replacing ESLint and Prettier.
Fly.io: platform for deploying applications globally (compatible with Hono).

## 1.3 Key Considerations

- The stack emphasizes serverless architecture and modern development practices.
- Remember to follow Hono's best practices for structuring your application and utilize Vitest for comprehensive testing.
- When working with Neon and Prisma, make sure to properly manage your database migrations and leverage Prisma's type-safe queries.
- Always use and maintain modules listed here /private/var/www/2025/ollamar1/beauty-crm/shared-platform-engineering and here  /private/var/www/2025/ollamar1/beauty-crm/shared-product-engineering. Always use intro...
```