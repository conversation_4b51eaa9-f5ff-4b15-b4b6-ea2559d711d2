```markdown
# 1. Authentication & User Management Roadmap

Fully use Clean Architecture and DDD

avoid use prisma in domain layer

Tech stack:
- Typescript
- Node.js
- Vite
- Vitest
- Tailwind CSS
- Hono

## 1.1 TaaS
Tenant as a Service

- [ ] 1.1.1. Create Tenant (or user pool) - M1
- [ ] 1.1.2. Delete Tenant (or user pool) - M2
- [ ] 1.1.3. Get Tenant (or user pool) - M3
- [ ] 1.1.4. Update Tenant (or user pool) - M4
- [ ] 1.1.5. Get Tenants (or user pools) - M5
- [ ] 1.1.6. Update Tenants (or user pools) - M6
- [ ] 1.1.7. Delete Tenants (or user pools) - M7
- [ ] 1.1.8. Tenant is a User Pool - M8
- [ ] 1.1.9. Tenant have a list of roles - M9
- [ ] 1.1.10. Role is a User Group - M10
- [ ] 1.1.11. Role have a list of policies - M11
- [ ] 1.1.12. Policy is a User Policy - M12
- [ ] 1.1.13. Policy have a list of actions - M13
- [ ] 1.1.14. Action is a User Action - M14
- [ ] 1.1.15. Action have a list of resources - M15
- [ ] 1.1.16. Resource is a User Resourc...

## 1.2 Appointment Service

### 1.2.1. Domain Library
- [ ] *******. Define Appointment Domain Model - M1
- [ ] *******. Implement Appointment Business Logic - M2
- [ ] 1.2.1.3. Define Appointment Use Cases - M3

### 1.2.2. Infrastructure Library
- [ ] 1.2.2.1. Implement Appointment Repository Interface - M1
- [ ] 1.2.2.2. Implement Appointment Repository Implementation - M2
- [ ] 1.2.2.3. Implement Appointment Service Interface - M3
- [ ] 1.2.2.4. Implement Appointment Service Implementation - M4

### 1.2.3. Controller Library
- [ ] 1.2.3.1. Implement Appointment Controller Interface - M1
- [ ] 1.2.3.2. Implement Appointment Controller Implementation - M2

## 2. Test Service

### 2.1. Setup
- [ ] 2.1.1. Create Test Service Directory - M1
- [ ] 2.1.2. Configure Prisma Schema - M2
- [ ] 2.1.3. Set up Hono Server - M3

### 2.2. Implementation
- [ ] 2.2.1. Implement `POST /appointments` Endpoint - M1
- [ ] 2.2.2. Implement Schema Validation - M2
- [ ] 2.2.3. Implement Business Logic - M3
- [ ] 2.2.4. Implement Database Saving - M4
- [ ] 2.2.5. Implement Response Handling - M5

### 2.3. Testing
- [ ] 2.3.1. Test Successful Appointment Creation - M1
- [ ] 2.3.2. Test Error Handling - M2

## 3. Package Dependencies

```json
{
  "dependencies": {
    "@beauty-crm/platform-appointment-unified": "workspace:*",
    "@prisma/client": "^6.11.1",
    "hono": "^4.8.3"
  }
}
```

## 4. Service Structure

```
services/appointment-test/
├── package.json
├── prisma/
│   └── schema.prisma (from schema library)
├── src/
│   ├── index.ts (Hono server)
│   ├── container.ts (DI setup)
│   └── routes/
│       └── appointments.ts (single endpoint)
└── .env (database URL)
```

## 5. Test Endpoint

```typescript
POST /appointments
{
  "salonId": "salon123",
  "customerId": "customer123", 
  "treatmentId": "treatment123",
  "customerName": "John Doe...
```