{
  "name": "Autonomous AI Coding Agent",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "coding-request",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-trigger",
      "name": "Coding Request Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "method": "POST",
        "url": "http://localhost:11434/api/generate",
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "model",
              "value": "gemma-3n-e4b-it"
            },
            {
              "name": "prompt",
              "value": "=You are an expert software architect working on the Beauty CRM project.\n\nSystem Instructions:\n- Be extremely conservative with confidence scores\n- Always provide structured JSON responses\n- Follow .augment-guidelines strictly\n- Analyze code changes for potential breaking impacts\n\nTask: {{ $json.task }}\nDescription: {{ $json.description }}\nFiles to modify: {{ $json.files }}\n\nProject Context:\n- 2500+ files in TypeScript/React/Node.js\n- Microservices architecture with 14 services\n- Uses Nx monorepo, Prisma, Docker, Tilt\n- Follow .augment-guidelines strictly\n\nProvide response as JSON:\n{\n  \"confidence\": 0.05,\n  \"riskLevel\": \"high\",\n  \"affectedFiles\": [\"list of files\"],\n  \"implementationPlan\": \"detailed plan\",\n  \"potentialIssues\": [\"list of issues\"],\n  \"reasoning\": \"why this confidence/risk level\"\n}\n\nBe extremely conservative. Default confidence should be 0.05 unless absolutely certain."
            },
            {
              "name": "options",
              "value": {
                "temperature": 0.1,
                "max_tokens": 4000,
                "top_p": 0.9,
                "stop": ["```javascript
", "---"]
              }
            },
            {
              "name": "stream",
              "value": false
            }
          ]
        },
        "options": {
          "timeout": 30000
        }
      },
      "id": "ai-analysis",
      "name": "AI Task Analysis (Gemma 3)",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [460, 300]
    },
    {
      "parameters": {
        "conditions": {
          "number": [
            {
              "value1": "={{ $json.confidence }}",
              "operation": "smaller",
              "value2": 0.1
            }
          ]
        }
      },
      "id": "confidence-check",
      "name": "Confidence Check",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [680, 300]
    },
    {
      "parameters": {
        "url": "http://supervisor-ui:3001/webhook/request-approval",
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "agentId",
              "value": "autonomous-coder-1"
            },
            {
              "name": "action",
              "value": "={{ $json.task }}"
            },
            {
              "name": "description",
              "value": "={{ $json.description }}"
            },
            {
              "name": "confidence",
              "value": "={{ $json.confidence }}"
            },
            {
              "name": "riskLevel",
              "value": "={{ $json.riskLevel }}"
            },
            {
              "name": "affectedFiles",
              "value": "={{ $json.affectedFiles }}"
            }
          ]
        }
      },
      "id": "request-approval",
      "name": "Request Human Approval",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [900, 200]
    },
    {
      "parameters": {
        "path": "approval-decision",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "approval-webhook",
      "name": "Approval Decision Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [1120, 200]
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{ $json.decision }}",
              "value2": "approve"
            }
          ]
        }
      },
      "id": "approval-check",
      "name": "Check Approval",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [1340, 200]
    },
    {
      "parameters": {
        "method": "POST",
        "url": "http://localhost:11434/api/generate",
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "model",
              "value": "gemma-3n-e4b-it"
            },
            {
              "name": "prompt",
              "value": "=You are a senior software developer generating code for the Beauty CRM project.\n\nSystem Instructions:\n- Generate production-ready, well-tested code\n- Follow .augment-guidelines strictly\n- Use existing codebase patterns\n- Include proper error handling and validation\n- Generate comprehensive unit tests\n- Follow TypeScript best practices\n- Use existing UI components from Introvertic UI\n\nTask: {{ $json.task }}\nImplementation Plan: {{ $json.implementationPlan }}\nAffected Files: {{ $json.affectedFiles }}\nRisk Level: {{ $json.riskLevel }}\n\nGenerate code following these rules:\n1. Follow .augment-guidelines strictly\n2. Use existing patterns from the codebase\n3. Include proper TypeScript types\n4. Add comprehensive error handling\n5. Include unit tests for all new functions\n6. Follow DDD patterns\n7. Use existing UI components\n8. Add proper logging and monitoring\n\nProvide response as valid JSON only:\n{\n  \"files\": [\n    {\n      \"path\": \"relative/path/to/file.ts\",\n      \"content\": \"complete file content with proper imports and exports\",\n      \"action\": \"create|modify|delete\"\n    }\n  ],\n  \"tests\": [\n    {\n      \"path\": \"relative/path/to/test.spec.ts\",\n      \"content\": \"complete test file content\",\n      \"action\": \"create|modify\"\n    }\n  ],\n  \"documentation\": [\n    {\n      \"path\": \"README.md or docs file\",\n      \"content\": \"updated documentation\",\n      \"action\": \"modify\"\n    }\n  ]\n}"
            },
            {
              "name": "options",
              "value": {
                "temperature": 0.15,
                "max_tokens": 8000,
                "top_p": 0.95,
                "stop": ["
```", "---", "END_CODE"]
              }
            },
            {
              "name": "stream",
              "value": false
            }
          ]
        },
        "options": {
          "timeout": 60000
        }
      },
      "id": "code-generation",
      "name": "Generate Code (Gemma 3)",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [900, 400]
    },
    {
      "parameters": {
        "url": "http://quality-gate:3002/webhook/pre-commit",
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "files",
              "value": "={{ $json.files.map(f => f.path) }}"
            },
            {
              "name": "agentId",
              "value": "autonomous-coder-1"
            }
          ]
        }
      },
      "id": "quality-gate",
      "name": "Quality Gate Validation",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [1120, 400]
    },
    {
      "parameters": {
        "conditions": {
          "boolean": [
            {
              "value1": "={{ $json.passed }}",
              "value2": true
            }
          ]
        }
      },
      "id": "quality-check",
      "name": "Quality Check",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [1340, 400]
    },
    {
      "parameters": {
        "command": "cd /workspace && git checkout -b agent-{{ $now.format('YYYY-MM-DD-HH-mm-ss') }}-{{ $json.task.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase() }}"
      },
      "id": "create-branch",
      "name": "Create Git Branch",
      "type": "n8n-nodes-base.executeCommand",
      "typeVersion": 1,
      "position": [1560, 400]
    },
    {
      "parameters": {
        "jsCode": "// Apply file changes to workspace\nconst fs = require('fs');\nconst path = require('path');\n\nconst workspacePath = '/workspace';\nconst changes = $input.first().json.files;\n\nfor (const change of changes) {\n  const fullPath = path.join(workspacePath, change.path);\n  \n  try {\n    if (change.action === 'create' || change.action === 'modify') {\n      // Ensure directory exists\n      const dir = path.dirname(fullPath);\n      fs.mkdirSync(dir, { recursive: true });\n      \n      // Write file content\n      fs.writeFileSync(fullPath, change.content, 'utf8');\n      console.log(`${change.action}d: ${change.path}`);\n    } else if (change.action === 'delete') {\n      if (fs.existsSync(fullPath)) {\n        fs.unlinkSync(fullPath);\n        console.log(`Deleted: ${change.path}`);\n      }\n    }\n  } catch (error) {\n    console.error(`Error processing ${change.path}:`, error.message);\n    throw error;\n  }\n}\n\nreturn { \n  success: true, \n  filesProcessed: changes.length,\n  changes: changes.map(c => ({ path: c.path, action: c.action }))\n};"
      },
      "id": "apply-changes",
      "name": "Apply File Changes",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [1780, 400]
    },
    {
      "parameters": {
        "command": "cd /workspace && git add . && git commit -m \"AI Agent: {{ $json.task }}\\n\\nGenerated by autonomous coding agent\\nConfidence: {{ $json.confidence }}\\nRisk Level: {{ $json.riskLevel }}\\nFiles modified: {{ $json.filesProcessed }}\""
      },
      "id": "git-commit",
      "name": "Git Commit",
      "type": "n8n-nodes-base.executeCommand",
      "typeVersion": 1,
      "position": [2000, 400]
    },
    {
      "parameters": {
        "url": "http://supervisor-ui:3001/webhook/agent-status",
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "agentId",
              "value": "autonomous-coder-1"
            },
            {
              "name": "status",
              "value": "completed"
            },
            {
              "name": "currentTask",
              "value": "={{ $json.task }}"
            },
            {
              "name": "progress",
              "value": 100
            }
          ]
        }
      },
      "id": "status-update",
      "name": "Update Agent Status",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [2220, 400]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"Task completed successfully\",\n  \"branch\": $json.branch,\n  \"filesModified\": $json.filesProcessed,\n  \"confidence\": $json.confidence,\n  \"riskLevel\": $json.riskLevel\n} }}"
      },
      "id": "success-response",
      "name": "Success Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [2440, 400]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ {\n  \"success\": false,\n  \"message\": \"Task rejected or failed validation\",\n  \"reason\": $json.reason || \"Quality gate failed or human rejected\"\n} }}"
      },
      "id": "failure-response",
      "name": "Failure Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [1340, 600]
    }
  ],
  "connections": {
    "Coding Request Webhook": {
      "main": [
        [
          {
            "node": "AI Task Analysis",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "AI Task Analysis": {
      "main": [
        [
          {
            "node": "Confidence Check",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Confidence Check": {
      "main": [
        [
          {
            "node": "Request Human Approval",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Generate Code",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Request Human Approval": {
      "main": [
        [
          {
            "node": "Approval Decision Webhook",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Approval Decision Webhook": {
      "main": [
        [
          {
            "node": "Check Approval",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check Approval": {
      "main": [
        [
          {
            "node": "Generate Code",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Failure Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Code": {
      "main": [
        [
          {
            "node": "Quality Gate Validation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Quality Gate Validation": {
      "main": [
        [
          {
            "node": "Quality Check",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Quality Check": {
      "main": [
        [
          {
            "node": "Create Git Branch",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Failure Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Create Git Branch": {
      "main": [
        [
          {
            "node": "Apply File Changes",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Apply File Changes": {
      "main": [
        [
          {
            "node": "Git Commit",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Git Commit": {
      "main": [
        [
          {
            "node": "Update Agent Status",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Update Agent Status": {
      "main": [
        [
          {
            "node": "Success Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": null,
  "tags": [
    {
      "createdAt": "2025-01-01T00:00:00.000Z",
      "updatedAt": "2025-01-01T00:00:00.000Z",
      "id": "autonomous-coding",
      "name": "autonomous-coding"
    }
  ],
  "triggerCount": 1,
  "updatedAt": "2025-01-01T00:00:00.000Z",
  "versionId": "1"
}
