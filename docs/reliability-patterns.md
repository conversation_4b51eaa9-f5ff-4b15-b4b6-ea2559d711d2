```markdown
# 1. Reliability Patterns Roadmap

Fully use Clean Architecture and DDD

avoid use prisma in domain layer

Tech stack:
- Typescript
- Node.js
- Vite
- Vitest
- Tailwind CSS

## 1.1 Circuit Breaker
- **Use for:** Protecting services from repeated failures of downstream dependencies.
- **Template:** [`pattern-templates/circuit-breaker.ts`](../shared-platform-engineering/pattern-templates/circuit-breaker.ts)
- **Example:**
```typescript
import { createCircuitBreaker } from '@beauty-crm/shared-platform-engineering/pattern-templates/circuit-breaker';

const breaker = createCircuitBreaker({
  failureThreshold: 5,
  recoveryTimeoutMs: 30000,
  onOpen: () => logger.warn('Circuit breaker opened'),
});

export async function fetchWithBreaker(url: string) {
  return breaker.execute(() => fetch(url));
}
```

## 1.2 Retry
- **Use for:** Retrying transient failures (e.g., network errors).
- **Template:** [`pattern-templates/retry.ts`](../shared-platform-engineering/pattern-templates/retry.ts)
- **Example:**
```typescript
import { retry } from '@beauty-crm/shared-platform-engineering/pattern-templates/retry';

export async function fetchWithRetry(url: string) {
  return retry(() => fetch(url), { retries: 3, delayMs: 500 });
}
```

## 1.3 Timeout
- **Use for:** Preventing operations from hanging indefinitely.
- **Template:** [`pattern-templates/timeout.ts`](../shared-platform-engineering/pattern-templates/timeout.ts)
- **Example:**
```typescript
import { withTimeout } from '@beauty-crm/shared-platform-engineering/pattern-templates/timeout';

export async function fetchWithTimeout(url: string) {
  return withTimeout(() => fetch(url), 2000); // 2s timeout
}
```

## 1.4 Idempotency
- **Use for:** Ensuring repeated requests have the same effect as a single request.
- **Template:** [`pattern-templates/idempotency.ts`](../shared-platform-engineering/pattern-templates/idempotency.ts)
- **Example:**
```types...
```