#!/usr/bin/env node

const axios = require('axios');

// Configuration
const SUPERVISOR_URL = 'http://localhost:3001';
const TASK_API_URL = 'http://localhost:3003';

// Mock AI agents with internship profiles
const MOCK_AGENTS = [
    {
        id: 'agent-intern-001',
        name: '<PERSON> (Documentation Intern)',
        specialization: 'documentation',
        experience: 'beginner',
        maxComplexity: 1,
        personality: 'careful and thorough',
        strengths: ['attention to detail', 'clear writing'],
        weaknesses: ['slow pace', 'needs guidance']
    },
    {
        id: 'agent-intern-002', 
        name: '<PERSON> (Frontend Intern)',
        specialization: 'frontend',
        experience: 'beginner',
        maxComplexity: 2,
        personality: 'creative but impulsive',
        strengths: ['UI design sense', 'quick learner'],
        weaknesses: ['makes mistakes', 'skips testing']
    },
    {
        id: 'agent-intern-003',
        name: '<PERSON> (Backend Intern)', 
        specialization: 'backend',
        experience: 'intermediate',
        maxComplexity: 3,
        personality: 'analytical and methodical',
        strengths: ['problem solving', 'code quality'],
        weaknesses: ['perfectionist', 'slow delivery']
    },
    {
        id: 'agent-intern-004',
        name: '<PERSON> (Testing Intern)',
        specialization: 'testing',
        experience: 'beginner',
        maxComplexity: 2,
        personality: 'detail-oriented but anxious',
        strengths: ['finds edge cases', 'thorough testing'],
        weaknesses: ['overthinks', 'needs reassurance']
    }
];

// Create and register agents
async function createAgents() {
    console.log('🤖 Creating mock AI agent interns...\n');
    
    for (const agent of MOCK_AGENTS) {
        try {
            // Register agent with supervisor
            await axios.post(`${SUPERVISOR_URL}/webhook/agent-status`, {
                agentId: agent.id,
                status: 'idle',
                currentTask: null,
                progress: 0,
                lastActivity: new Date().toISOString(),
                agentInfo: agent
            });
            
            console.log(`✅ Created: ${agent.name}`);
            console.log(`   Specialization: ${agent.specialization}`);
            console.log(`   Experience: ${agent.experience}`);
            console.log(`   Max Complexity: ${agent.maxComplexity}`);
            console.log(`   Strengths: ${agent.strengths.join(', ')}`);
            console.log(`   Areas for improvement: ${agent.weaknesses.join(', ')}\n`);
            
        } catch (error) {
            console.error(`❌ Failed to create ${agent.name}:`, error.message);
        }
    }
}

// Get suitable tasks for agents
async function getSuitableTasks() {
    try {
        console.log('📋 Fetching suitable tasks for interns...');
        
        // Get low-risk, simple tasks
        const response = await axios.get(`${TASK_API_URL}/api/tasks?priority=low&completed=false&riskLevel=low`);
        
        if (response.data.success) {
            const tasks = response.data.tasks.filter(task => 
                task.estimatedComplexity <= 2 && 
                !task.isCompleted
            ).slice(0, 10); // Get first 10 suitable tasks
            
            console.log(`✅ Found ${tasks.length} suitable tasks for interns`);
            return tasks;
        }
        
        return [];
    } catch (error) {
        console.error('❌ Failed to fetch tasks:', error.message);
        return [];
    }
}

// Simulate agent working on a task
async function simulateAgentWork(agent, task) {
    console.log(`\n🔄 ${agent.name} starting work on: "${task.text.substring(0, 50)}..."`);
    
    try {
        // Update agent status to working
        await axios.post(`${SUPERVISOR_URL}/webhook/agent-status`, {
            agentId: agent.id,
            status: 'working',
            currentTask: task.text,
            progress: 0,
            lastActivity: new Date().toISOString()
        });
        
        // Simulate work progress
        const workDuration = Math.random() * 30000 + 10000; // 10-40 seconds
        const progressSteps = 5;
        const stepDuration = workDuration / progressSteps;
        
        for (let step = 1; step <= progressSteps; step++) {
            await new Promise(resolve => setTimeout(resolve, stepDuration));
            
            const progress = (step / progressSteps) * 100;
            await axios.post(`${SUPERVISOR_URL}/webhook/agent-status`, {
                agentId: agent.id,
                status: 'working',
                currentTask: task.text,
                progress: progress,
                lastActivity: new Date().toISOString()
            });
            
            console.log(`   📊 ${agent.name}: ${Math.round(progress)}% complete`);
        }
        
        // Simulate task completion with realistic performance
        const performance = generateRealisticPerformance(agent, task);
        
        await axios.post(`${SUPERVISOR_URL}/webhook/agent-performance`, {
            agentId: agent.id,
            taskCompleted: true,
            successful: performance.successful,
            timeSpent: Math.round(workDuration / 1000), // in seconds
            codeQuality: performance.codeQuality,
            instructionFollowing: performance.instructionFollowing,
            hadError: performance.hadError,
            taskDetails: {
                taskId: task.id,
                taskText: task.text,
                complexity: task.estimatedComplexity
            }
        });
        
        // Update agent status to idle
        await axios.post(`${SUPERVISOR_URL}/webhook/agent-status`, {
            agentId: agent.id,
            status: 'idle',
            currentTask: null,
            progress: 100,
            lastActivity: new Date().toISOString()
        });
        
        const result = performance.successful ? '✅ SUCCESS' : '❌ FAILED';
        console.log(`   ${result}: ${agent.name} completed task`);
        console.log(`   Quality: ${performance.codeQuality}/100, Instructions: ${performance.instructionFollowing}/100`);
        
        return performance;
        
    } catch (error) {
        console.error(`❌ Error simulating work for ${agent.name}:`, error.message);
        return null;
    }
}

// Generate realistic performance based on agent characteristics
function generateRealisticPerformance(agent, task) {
    const baseSuccess = agent.experience === 'beginner' ? 0.7 : 0.85;
    const complexityPenalty = task.estimatedComplexity > agent.maxComplexity ? 0.3 : 0;
    
    const successRate = Math.max(0.1, baseSuccess - complexityPenalty + (Math.random() * 0.2 - 0.1));
    const successful = Math.random() < successRate;
    
    // Quality scores based on agent strengths/weaknesses
    let codeQuality = 50 + Math.random() * 30; // Base 50-80
    let instructionFollowing = 50 + Math.random() * 30; // Base 50-80
    
    // Adjust based on agent characteristics
    if (agent.strengths.includes('attention to detail')) {
        codeQuality += 15;
        instructionFollowing += 10;
    }
    if (agent.strengths.includes('code quality')) {
        codeQuality += 20;
    }
    if (agent.weaknesses.includes('makes mistakes')) {
        codeQuality -= 15;
        instructionFollowing -= 10;
    }
    if (agent.weaknesses.includes('needs guidance')) {
        instructionFollowing -= 15;
    }
    
    // Cap scores
    codeQuality = Math.max(10, Math.min(100, codeQuality));
    instructionFollowing = Math.max(10, Math.min(100, instructionFollowing));
    
    const hadError = !successful || Math.random() < 0.2;
    
    return {
        successful,
        codeQuality: Math.round(codeQuality),
        instructionFollowing: Math.round(instructionFollowing),
        hadError
    };
}

// Main simulation function
async function runAgentSimulation() {
    console.log('🎯 Starting AI Agent Internship Simulation\n');
    
    // Create agents
    await createAgents();
    
    // Wait for agents to register
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Get tasks
    const tasks = await getSuitableTasks();
    
    if (tasks.length === 0) {
        console.log('❌ No suitable tasks found for agents');
        return;
    }
    
    console.log('\n🚀 Starting agent work simulation...');
    
    // Assign tasks to agents
    let taskIndex = 0;
    const workPromises = [];
    
    for (const agent of MOCK_AGENTS) {
        if (taskIndex < tasks.length) {
            const task = tasks[taskIndex];
            
            // Record task assignment
            await axios.post(`${SUPERVISOR_URL}/webhook/agent-performance`, {
                agentId: agent.id,
                taskAssigned: true,
                taskDetails: {
                    taskId: task.id,
                    taskText: task.text,
                    complexity: task.estimatedComplexity
                }
            });
            
            // Start work (with delay to stagger)
            const workPromise = new Promise(resolve => {
                setTimeout(async () => {
                    const result = await simulateAgentWork(agent, task);
                    resolve(result);
                }, taskIndex * 5000); // 5 second delay between starts
            });
            
            workPromises.push(workPromise);
            taskIndex++;
        }
    }
    
    // Wait for all work to complete
    await Promise.all(workPromises);
    
    console.log('\n🎉 Agent simulation complete!');
    console.log('\n📊 Check the supervisor dashboard to see agent grades and performance:');
    console.log('   🎓 Supervisor: http://localhost:3001');
    console.log('   📋 Tasks: http://localhost:3003');
}

// Run simulation
if (require.main === module) {
    runAgentSimulation().catch(console.error);
}

module.exports = { createAgents, simulateAgentWork };
