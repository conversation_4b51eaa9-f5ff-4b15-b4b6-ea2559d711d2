version: '3.8'

services:
  # n8n Workflow Automation Platform
  n8n:
    image: n8nio/n8n:latest
    container_name: beauty-crm-n8n
    restart: unless-stopped
    environment:
      # Local development configuration
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - N8N_EDITOR_BASE_URL=http://localhost:5678
      - WEBHOOK_URL=http://localhost:5678/
      - GENERIC_TIMEZONE=UTC
      - N8N_LOG_LEVEL=info

      # Disable auth for local development
      - N8N_BASIC_AUTH_ACTIVE=false
      - N8N_USER_MANAGEMENT_DISABLED=true
      - N8N_DIAGNOSTICS_ENABLED=false
      - N8N_VERSION_NOTIFICATIONS_ENABLED=false
      - N8N_TEMPLATES_ENABLED=false
      - N8N_ONBOARDING_FLOW_DISABLED=true
      - N8N_PERSONALIZATION_ENABLED=false
      - N8N_SECURE_COOKIE=false

      # Local SQLite database for simplicity
      - DB_TYPE=sqlite
      - DB_SQLITE_DATABASE=/data/database.sqlite

      # AI Agent Configuration
      - N8N_AI_ENABLED=true
      - GEMINI_API_KEY=${GEMINI_API_KEY}

      # Local workspace configuration
      - WORKSPACE_ROOT=/workspace
      - N8N_DEFAULT_BINARY_DATA_MODE=filesystem
      - N8N_BINARY_DATA_STORAGE_PATH=/data/binary-data

      # Execution settings for local file operations
      - EXECUTIONS_PROCESS=main
      - EXECUTIONS_MODE=regular
      - EXECUTIONS_TIMEOUT=3600
      - N8N_PAYLOAD_SIZE_MAX=16
    ports:
      - "5678:5678"
    volumes:
      # Local data persistence
      - ./data/n8n:/data:rw
      - ./workflows:/home/<USER>/.n8n/workflows:rw
      - ./credentials:/home/<USER>/.n8n/credentials:rw
      - ./nodes:/home/<USER>/.n8n/nodes:rw

      # CRITICAL: Mount workspace with READ-WRITE access for file operations
      - /private/var/www/2025/ollamar1/beauty-crm:/workspace:rw

      # Mount git for version control operations
      - /usr/bin/git:/usr/bin/git:ro
      - /usr/bin/node:/usr/bin/node:ro
      - /usr/bin/npm:/usr/bin/npm:ro

    # Remove database dependencies for local SQLite
    # depends_on:
    #   - n8n-postgres
    #   - redis
    networks:
      - beauty_crm_ai_agents
      - beauty_crm_traefik-public
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.n8n.rule=Host(`n8n.beauty-crm.localhost`)"
      - "traefik.http.services.n8n.loadbalancer.server.port=5678"
      - "traefik.docker.network=beauty_crm_traefik-public"

  # PostgreSQL Database for n8n
  n8n-postgres:
    image: postgres:15-alpine
    container_name: beauty-crm-n8n-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=n8n_secure_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - n8n_postgres_data:/var/lib/postgresql/data
    networks:
      - beauty_crm_ai_agents

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: beauty-crm-n8n-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_secure_password
    volumes:
      - n8n_redis_data:/data
    networks:
      - beauty_crm_ai_agents

  # AI Agent Supervisor - Human oversight interface
  supervisor-ui:
    image: node:18-alpine
    container_name: beauty-crm-supervisor
    restart: unless-stopped
    working_dir: /app
    command: sh -c "npm install && npm run dev"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - N8N_API_URL=http://n8n:5678
      - REDIS_URL=redis://redis:6379
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    ports:
      - "3001:3001"
    volumes:
      - ./supervisor-ui:/app
      - /private/var/www/2025/ollamar1/beauty-crm:/workspace:ro
    depends_on:
      - n8n
      - redis
    networks:
      - beauty_crm_ai_agents
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.supervisor.rule=Host(`supervisor.beauty-crm.localhost`)"
      - "traefik.http.services.supervisor.loadbalancer.server.port=3001"

  # Code Quality Gate - Automated validation service
  quality-gate:
    image: node:18-alpine
    container_name: beauty-crm-quality-gate
    restart: unless-stopped
    working_dir: /app
    command: sh -c "npm install && npm start"
    environment:
      - NODE_ENV=production
      - PORT=3002
      - WORKSPACE_PATH=/workspace
      - N8N_WEBHOOK_URL=http://n8n:5678/webhook
    volumes:
      - ./quality-gate:/app
      - /private/var/www/2025/ollamar1/beauty-crm:/workspace
    networks:
      - beauty_crm_ai_agents

  # Markdown Task Analyzer - Discovers tasks from README files
  markdown-analyzer:
    image: node:18-alpine
    container_name: beauty-crm-markdown-analyzer
    restart: unless-stopped
    working_dir: /app
    command: sh -c "npm install && npm start"
    environment:
      - NODE_ENV=production
      - PORT=3003
      - WORKSPACE_PATH=/workspace
      - N8N_WEBHOOK_URL=http://n8n:5678
      - SCAN_INTERVAL=0 */30 * * * *
    ports:
      - "3003:3003"
    volumes:
      - ./markdown-analyzer:/app
      - /private/var/www/2025/ollamar1/beauty-crm:/workspace:ro
    depends_on:
      - n8n
    networks:
      - beauty_crm_ai_agents
      - beauty_crm_traefik-public
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.markdown-analyzer.rule=Host(`tasks.beauty-crm.localhost`)"
      - "traefik.http.services.markdown-analyzer.loadbalancer.server.port=3003"

  # Git Operations Service - Safe repository management
  git-ops:
    image: alpine/git:latest
    container_name: beauty-crm-git-ops
    restart: unless-stopped
    working_dir: /workspace
    command: tail -f /dev/null
    environment:
      - GIT_AUTHOR_NAME=AI Agent System
      - GIT_AUTHOR_EMAIL=<EMAIL>
      - GIT_COMMITTER_NAME=AI Agent System
      - GIT_COMMITTER_EMAIL=<EMAIL>
    volumes:
      - /private/var/www/2025/ollamar1/beauty-crm:/workspace
      - ./git-hooks:/workspace/.git/hooks
    networks:
      - beauty_crm_ai_agents

volumes:
  n8n_data:
    driver: local
  n8n_postgres_data:
    driver: local
  n8n_redis_data:
    driver: local

networks:
  beauty_crm_ai_agents:
    driver: bridge
    external: false
  beauty_crm_traefik-public:
    external: true
