# 1. Authentication & User Management Roadmap

Fully use Clean Architecture and DDD

avoid use prisma in domain layer

Tech stack:
- Typescript
- Node.js
- Vite
- Vitest
- Tailwind CSS

## 1.1 Responses Package

Used to create responses for the API Flyio and shared errors, error messages, and response codes.

## 1.2 Error Handling

- [ ] 1.2.1. Define shared error types - M1
- [ ] 1.2.2. Implement error response codes - M2
- [ ] 1.2.3. Create error message templates - M3
- [ ] 1.2.4. Implement global error handling middleware - M4
- [ ] 1.2.5. Document error handling strategy - M5

## 1.3 User Management

- [ ] 1.3.1. Create Tenant (or user pool) - M1
- [ ] 1.3.2. Delete Tenant (or user pool) - M2
- [ ] 1.3.3. Get Tenant (or user pool) - M3
- [ ] 1.3.4. Update Tenant (or user pool) - M4
- [ ] 1.3.5. Get Tenants (or user pools) - M5
- [ ] 1.3.6. Update Tenants (or user pools) - M6
- [ ] 1.3.7. Delete Tenants (or user pools) - M7
- [ ] 1.3.8. Tenant is a User Pool - M8
- [ ] 1.3.9. Tenant have a list of roles - M9
- [ ] 1.3.10. Role is a User Group - M10
- [ ] 1.3.11. Role have a list of policies - M11
- [ ] 1.3.12. Policy is a User Policy - M12
- [ ] 1.3.13. Policy have a list of actions - M13
- [ ] 1.3.14. Action is a User Action - M14
- [ ] 1.3.15. Action have a list of resources - M15
- [ ] 1.3.16. Resource is a User Resourc...