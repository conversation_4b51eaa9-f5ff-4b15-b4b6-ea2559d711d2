version: '3.8'

services:
  # n8n Workflow Automation Platform
  n8n:
    image: n8nio/n8n:latest
    container_name: beauty-crm-n8n
    restart: unless-stopped
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=secure_password_change_me
      - N8N_HOST=n8n.beauty-crm.localhost
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://n8n.beauty-crm.localhost
      - GENERIC_TIMEZONE=UTC
      - N8N_LOG_LEVEL=debug
      - N8N_METRICS=true
      - N8N_DIAGNOSTICS_ENABLED=true
      # Database connection
      - DB_TYPE=postgres
      - DB_POSTGRESDB_HOST=n8n-postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=n8n_secure_password
      # AI Agent Configuration
      - N8N_AI_ENABLED=true
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      # Augment Code Integration
      - AUGMENT_CODE_API_KEY=${AUGMENT_CODE_API_KEY}
      - AUGMENT_CODE_WORKSPACE=/workspace
    ports:
      - "5678:5678"
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - /private/var/www/2025/ollamar1/beauty-crm:/workspace:ro
      - ./workflows:/home/<USER>/.n8n/workflows
      - ./credentials:/home/<USER>/.n8n/credentials
      - ./nodes:/home/<USER>/.n8n/nodes
    depends_on:
      - n8n-postgres
      - redis
    networks:
      - beauty_crm_ai_agents
      - beauty_crm_traefik-public
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.n8n.rule=Host(`n8n.beauty-crm.localhost`)"
      - "traefik.http.services.n8n.loadbalancer.server.port=5678"
      - "traefik.docker.network=beauty_crm_traefik-public"

  # PostgreSQL Database for n8n
  n8n-postgres:
    image: postgres:15-alpine
    container_name: beauty-crm-n8n-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=n8n_secure_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - n8n_postgres_data:/var/lib/postgresql/data
    networks:
      - beauty_crm_ai_agents

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: beauty-crm-n8n-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_secure_password
    volumes:
      - n8n_redis_data:/data
    networks:
      - beauty_crm_ai_agents

  # AI Agent Supervisor - Human oversight interface
  supervisor-ui:
    image: node:18-alpine
    container_name: beauty-crm-supervisor
    restart: unless-stopped
    working_dir: /app
    command: sh -c "npm install && npm run dev"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - N8N_API_URL=http://n8n:5678
      - REDIS_URL=redis://redis:6379
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    ports:
      - "3001:3001"
    volumes:
      - ./supervisor-ui:/app
      - /private/var/www/2025/ollamar1/beauty-crm:/workspace:ro
    depends_on:
      - n8n
      - redis
    networks:
      - beauty_crm_ai_agents
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.supervisor.rule=Host(`supervisor.beauty-crm.localhost`)"
      - "traefik.http.services.supervisor.loadbalancer.server.port=3001"

  # Code Quality Gate - Automated validation service
  quality-gate:
    image: node:18-alpine
    container_name: beauty-crm-quality-gate
    restart: unless-stopped
    working_dir: /app
    command: sh -c "npm install && npm start"
    environment:
      - NODE_ENV=production
      - PORT=3002
      - WORKSPACE_PATH=/workspace
      - N8N_WEBHOOK_URL=http://n8n:5678/webhook
    volumes:
      - ./quality-gate:/app
      - /private/var/www/2025/ollamar1/beauty-crm:/workspace
    networks:
      - beauty_crm_ai_agents

  # Markdown Task Analyzer - Discovers tasks from README files
  markdown-analyzer:
    image: node:18-alpine
    container_name: beauty-crm-markdown-analyzer
    restart: unless-stopped
    working_dir: /app
    command: sh -c "npm install && npm start"
    environment:
      - NODE_ENV=production
      - PORT=3003
      - WORKSPACE_PATH=/workspace
      - N8N_WEBHOOK_URL=http://n8n:5678
      - SCAN_INTERVAL=0 */30 * * * *
    ports:
      - "3003:3003"
    volumes:
      - ./markdown-analyzer:/app
      - /private/var/www/2025/ollamar1/beauty-crm:/workspace:ro
    depends_on:
      - n8n
    networks:
      - beauty_crm_ai_agents
      - beauty_crm_traefik-public
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.markdown-analyzer.rule=Host(`tasks.beauty-crm.localhost`)"
      - "traefik.http.services.markdown-analyzer.loadbalancer.server.port=3003"

  # Git Operations Service - Safe repository management
  git-ops:
    image: alpine/git:latest
    container_name: beauty-crm-git-ops
    restart: unless-stopped
    working_dir: /workspace
    command: tail -f /dev/null
    environment:
      - GIT_AUTHOR_NAME=AI Agent System
      - GIT_AUTHOR_EMAIL=<EMAIL>
      - GIT_COMMITTER_NAME=AI Agent System
      - GIT_COMMITTER_EMAIL=<EMAIL>
    volumes:
      - /private/var/www/2025/ollamar1/beauty-crm:/workspace
      - ./git-hooks:/workspace/.git/hooks
    networks:
      - beauty_crm_ai_agents

volumes:
  n8n_data:
    driver: local
  n8n_postgres_data:
    driver: local
  n8n_redis_data:
    driver: local

networks:
  beauty_crm_ai_agents:
    driver: bridge
    external: false
  beauty_crm_traefik-public:
    external: true
