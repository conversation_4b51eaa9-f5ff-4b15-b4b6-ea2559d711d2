# 1. Authentication & User Management Roadmap

Fully use Clean Architecture and DDD

avoid use prisma in domain layer

Tech stack:
- Typescript
- Node.js
- Vite
- Vitest
- Tailwind CSS

## 1.1 TaaS
Tenant as a Service

- [ ] 1.1.1. Create Tenant (or user pool) - M1
- [ ] 1.1.2. Delete Tenant (or user pool) - M2
- [ ] 1.1.3. Get Tenant (or user pool) - M3
- [ ] 1.1.4. Update Tenant (or user pool) - M4
- [ ] 1.1.5. Get Tenants (or user pools) - M5
- [ ] 1.1.6. Update Tenants (or user pools) - M6
- [ ] 1.1.7. Delete Tenants (or user pools) - M7
- [ ] 1.1.8. Tenant is a User Pool - M8
- [ ] 1.1.9. Tenant have a list of roles - M9
- [ ] 1.1.10. Role is a User Group - M10
- [ ] 1.1.11. Role have a list of policies - M11
- [ ] 1.1.12. Policy is a User Policy - M12
- [ ] 1.1.13. Policy have a list of actions - M13
- [ ] 1.1.14. Action is a User Action - M14
- [ ] 1.1.15. Action have a list of resources - M15
- [ ] 1.1.16. Resource is a User Resourc...

## 1. Project Vision & Goals

The overarching vision is to provide a seamless, efficient, and reliable appointment management experience for salon owners, staff, and clients. Key goals include:

- [ ] **Real-Time Synchronization:** Appointments booked through client-facing systems (Planner Frontend) must be immediately visible and manageable in the salon management calendar (Management Frontend).
- [x] **Unified Data Models:** Standardize appointment-related data across all systems to ensure consistency and reduce integration complexity.
- [ ] **Operational Efficiency:** Reduce manual tasks, minimize scheduling conflicts, and improve staff productivity through automated, real-time updates.
- [ ] **Enhanced User Experience (UMUX Focus):** Address critical usability issues, particularly mobile responsiveness, workflow efficiency, and clear error handling, as highlighted by UMUX assessments (average 60.5% UMUX score, with responsiveness at 45.5%).
- [ ] **Scalability & Reliability:** Build a fault-tolerant, high-performance eventing infrastructure capable of supporting future growth and international expansion.
- [ ] **Compliance:** Ensure adherence to local regulations (e.g., KvK verification in Net...