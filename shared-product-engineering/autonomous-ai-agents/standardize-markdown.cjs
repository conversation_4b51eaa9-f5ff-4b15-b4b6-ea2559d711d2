#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const WORKSPACE_ROOT = '/private/var/www/2025/ollamar1/beauty-crm';
const TEMPLATE_FILE = path.join(WORKSPACE_ROOT, 'services/public-identity/public-identity-management-backend/ReadmeRoadmap.md');
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

if (!GEMINI_API_KEY) {
    console.error('❌ GEMINI_API_KEY environment variable not set');
    process.exit(1);
}

// Find all legitimate markdown files
function findMarkdownFiles() {
    const command = `find ${WORKSPACE_ROOT} -name "*.md" -type f \\
        -not -path "*/node_modules/*" \\
        -not -path "*/.*/*" \\
        -not -path "*/dist/*" \\
        -not -path "*/build/*" \\
        -not -path "*/coverage/*" \\
        -not -path "*/tmp/*" \\
        -not -path "*/temp/*" \\
        -not -path "*/logs/*" \\
        -not -path "*/log/*" \\
        -not -path "*/target/*" \\
        -not -path "*/out/*" \\
        -not -path "*/bin/*" \\
        -not -path "*/obj/*" \\
        -not -path "*/shared-product-engineering/autonomous-ai-agents/*"`;
    
    try {
        const output = execSync(command, { encoding: 'utf8' });
        return output.trim().split('\n').filter(file => file.length > 0);
    } catch (error) {
        console.error('Error finding markdown files:', error.message);
        return [];
    }
}

// Read template file
function readTemplate() {
    try {
        return fs.readFileSync(TEMPLATE_FILE, 'utf8');
    } catch (error) {
        console.error('❌ Error reading template file:', error.message);
        process.exit(1);
    }
}

// Analyze file content and generate standardized version
async function standardizeMarkdownFile(filePath, originalContent, template) {
    const relativePath = path.relative(WORKSPACE_ROOT, filePath);
    const fileName = path.basename(filePath);
    const dirName = path.dirname(relativePath);
    
    // Determine service/component name from path
    const pathParts = relativePath.split('/');
    let serviceName = 'Unknown Service';
    let componentType = 'Documentation';
    
    if (pathParts.includes('services')) {
        const serviceIndex = pathParts.indexOf('services');
        if (pathParts[serviceIndex + 1]) {
            serviceName = pathParts[serviceIndex + 1].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }
    } else if (pathParts.includes('docs')) {
        serviceName = 'Documentation';
        componentType = 'Knowledge Base';
    } else if (pathParts.includes('shared-product-engineering')) {
        serviceName = 'Shared Engineering';
        componentType = 'Library';
    }
    
    const prompt = `You are a documentation standardizer for the Beauty CRM project.

TEMPLATE STRUCTURE (use this as reference):
${template.substring(0, 1000)}...

TASK: Standardize this markdown file to follow the template structure.

FILE PATH: ${relativePath}
SERVICE: ${serviceName}
COMPONENT: ${componentType}

ORIGINAL CONTENT:
${originalContent.substring(0, 2000)}${originalContent.length > 2000 ? '...' : ''}

INSTRUCTIONS:
1. Create a standardized markdown file following the template structure
2. Extract existing tasks/todos and convert them to checkbox format: - [ ] Task description
3. Organize content into logical sections with numbered headings
4. Add tech stack section if missing
5. Convert any existing bullet points to actionable checkbox tasks
6. Maintain original technical content but improve structure
7. Add milestone markers (M1, M2, etc.) to tasks
8. Keep the same technical depth but improve readability

Respond with ONLY the standardized markdown content, no explanations.`;

    try {
        const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemma-3n-e4b-it:generateContent', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-goog-api-key': GEMINI_API_KEY
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.2,
                    maxOutputTokens: 4000,
                    topP: 0.9
                }
            })
        });

        if (!response.ok) {
            throw new Error(`API request failed: ${response.status}`);
        }

        const data = await response.json();
        const generatedContent = data.candidates?.[0]?.content?.parts?.[0]?.text;
        
        if (!generatedContent) {
            throw new Error('No content generated');
        }

        return generatedContent.trim();
    } catch (error) {
        console.error(`❌ Error standardizing ${relativePath}:`, error.message);
        return null;
    }
}

// Main function
async function main() {
    console.log('🔍 Finding markdown files...');
    const markdownFiles = findMarkdownFiles();
    console.log(`📋 Found ${markdownFiles.length} markdown files`);
    
    console.log('📖 Reading template...');
    const template = readTemplate();
    
    let processed = 0;
    let errors = 0;
    let skipped = 0;
    
    // Process files in batches to avoid rate limiting
    const BATCH_SIZE = 5;
    const DELAY_MS = 2000; // 2 second delay between batches
    
    for (let i = 0; i < markdownFiles.length; i += BATCH_SIZE) {
        const batch = markdownFiles.slice(i, i + BATCH_SIZE);
        
        console.log(`\n📦 Processing batch ${Math.floor(i/BATCH_SIZE) + 1}/${Math.ceil(markdownFiles.length/BATCH_SIZE)}`);
        
        const promises = batch.map(async (filePath) => {
            try {
                // Skip the template file itself
                if (filePath === TEMPLATE_FILE) {
                    console.log(`⏭️  Skipping template file: ${path.relative(WORKSPACE_ROOT, filePath)}`);
                    skipped++;
                    return;
                }
                
                const originalContent = fs.readFileSync(filePath, 'utf8');
                
                // Skip very small files (likely not real documentation)
                if (originalContent.length < 50) {
                    console.log(`⏭️  Skipping small file: ${path.relative(WORKSPACE_ROOT, filePath)}`);
                    skipped++;
                    return;
                }
                
                console.log(`🔄 Processing: ${path.relative(WORKSPACE_ROOT, filePath)}`);
                
                const standardizedContent = await standardizeMarkdownFile(filePath, originalContent, template);
                
                if (standardizedContent) {
                    // Create backup
                    const backupPath = filePath + '.backup';
                    fs.writeFileSync(backupPath, originalContent);
                    
                    // Write standardized content
                    fs.writeFileSync(filePath, standardizedContent);
                    
                    console.log(`✅ Standardized: ${path.relative(WORKSPACE_ROOT, filePath)}`);
                    processed++;
                } else {
                    console.log(`❌ Failed: ${path.relative(WORKSPACE_ROOT, filePath)}`);
                    errors++;
                }
            } catch (error) {
                console.error(`❌ Error processing ${filePath}:`, error.message);
                errors++;
            }
        });
        
        await Promise.all(promises);
        
        // Delay between batches to avoid rate limiting
        if (i + BATCH_SIZE < markdownFiles.length) {
            console.log(`⏳ Waiting ${DELAY_MS/1000}s before next batch...`);
            await new Promise(resolve => setTimeout(resolve, DELAY_MS));
        }
    }
    
    console.log('\n📊 STANDARDIZATION COMPLETE');
    console.log(`✅ Processed: ${processed}`);
    console.log(`⏭️  Skipped: ${skipped}`);
    console.log(`❌ Errors: ${errors}`);
    console.log(`📁 Total files: ${markdownFiles.length}`);
    
    // Trigger task discovery scan
    console.log('\n🔍 Triggering task discovery scan...');
    try {
        const response = await fetch('http://localhost:3003/api/tasks/scan', {
            method: 'POST'
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ Task discovery scan completed');
            console.log(`📋 New tasks discovered: ${result.results?.newTasks || 0}`);
        } else {
            console.log('⚠️  Task discovery scan failed - run manually');
        }
    } catch (error) {
        console.log('⚠️  Could not trigger task discovery scan - run manually');
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { findMarkdownFiles, standardizeMarkdownFile };
