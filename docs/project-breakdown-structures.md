```markdown
# 1. Authentication & User Management Roadmap

Fully use Clean Architecture and DDD

avoid use prisma in domain layer

Tech stack:
- Typescript
- Node.js
- Vite
- Vitest
- Tailwind CSS

## 1.1 TaaS
Tenant as a Service

- [ ] 1.1.1. Create Tenant (or user pool) - M1
- [ ] 1.1.2. Delete Tenant (or user pool) - M2
- [ ] 1.1.3. Get Tenant (or user pool) - M3
- [ ] 1.1.4. Update Tenant (or user pool) - M4
- [ ] 1.1.5. Get Tenants (or user pools) - M5
- [ ] 1.1.6. Update Tenants (or user pools) - M6
- [ ] 1.1.7. Delete Tenants (or user pools) - M7
- [ ] 1.1.8. Tenant is a User Pool - M8
- [ ] 1.1.9. Tenant have a list of roles - M9
- [ ] 1.1.10. Role is a User Group - M10
- [ ] 1.1.11. Role have a list of policies - M11
- [ ] 1.1.12. Policy is a User Policy - M12
- [ ] 1.1.13. Policy have a list of actions - M13
- [ ] 1.1.14. Action is a User Action - M14
- [ ] 1.1.15. Action have a list of resources - M15
- [ ] 1.1.16. Resource is a User Resourc...

## 2. Salon Management UI

- [ ] 2.1 Appointment Management
    - [ ] 2.1.1 Calendar View
    - [ ] 2.1.2 Booking Interface
    - [ ] 2.1.3 Appointment Status
    - [ ] 2.1.4 Staff Allocation
- [ ] 2.2 Customer Management
    - [ ] 2.2.1 Customer Profiles
    - [ ] 2.2.2 Service History
    - [ ] 2.2.3 Preferences
    - [ ] 2.2.4 Communication Logs
- [ ] 2.3 Resource Management
    - [ ] 2.3.1 Staff Management
    - [ ] 2.3.2 Room Scheduling
    - [ ] 2.3.3 Equipment Tracking
    - [ ] 2.3.4 Availability Calendar
- [ ] 2.4 Inventory Management
    - [ ] 2.4.1 Product Catalog
    - [ ] 2.4.2 Stock Levels
    - [ ] 2.4.3 Supplier Management
    - [ ] 2.4.4 Usage Tracking
- [ ] 2.5 Business Intelligence
    - [ ] 2.5.1 Sales Reports
    - [ ] 2.5.2 Resource Utilization
    - [ ] 2.5.3 Customer Analytics
    - [ ] 2.5.4 Performance Dashboards

## 3. UI Components

- [ ] 3.1 Appointment Modules
    - [ ] 3.1.1 Calendar Component
    - [ ] 3.1.2 Booking Wizard
    - [ ] 3.1.3 Appointment Details
    - [ ] 3.1.4 Resource Scheduler
- [ ] 3.2 Customer Modules
    - [ ] 3.2.1 Customer Profile Editor
    - [ ] 3.2.2 Service History
    - [ ] 3.2.3 Preference Center
    - [ ] 3.2.4 Communication Logs
- [ ] 3.3 Resource Modules
    - [ ] 3.3.1 Staff Management
    - [ ] 3.3.2 Room Management
    - [ ] 3.3.3 Equipment Tracker
    - [ ] 3.3.4 Availability Manager
- [ ] 3.4 Inventory Modules
    - [ ] 3.4.1 Product Catalog
    - [ ] 3.4.2 Stock Manager
    - [ ] 3.4.3 Supplier Directory
    - [ ] 3.4.4 Restock Alerts
- [ ] 3.5 Analytics Modules
    - [ ] 3.5.1 Report Generator
    - [ ] 3.5.2 Resource Utilization
    - [ ] 3.5.3 Customer An...
```