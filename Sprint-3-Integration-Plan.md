```markdown
# 1. Authentication & User Management Roadmap

Fully use Clean Architecture and DDD

avoid use prisma in domain layer

Tech stack:
- Typescript
- Node.js
- Vite
- Vitest
- Tailwind CSS

## 1.1 TaaS
Tenant as a Service

- [ ] 1.1.1. Create Tenant (or user pool) - M1
- [ ] 1.1.2. Delete Tenant (or user pool) - M2
- [ ] 1.1.3. Get Tenant (or user pool) - M3
- [ ] 1.1.4. Update Tenant (or user pool) - M4
- [ ] 1.1.5. Get Tenants (or user pools) - M5
- [ ] 1.1.6. Update Tenants (or user pools) - M6
- [ ] 1.1.7. Delete Tenants (or user pools) - M7
- [ ] 1.1.8. Tenant is a User Pool - M8
- [ ] 1.1.9. Tenant have a list of roles - M9
- [ ] 1.1.10. Role is a User Group - M10
- [ ] 1.1.11. Role have a list of policies - M11
- [ ] 1.1.12. Policy is a User Policy - M12
- [ ] 1.1.13. Policy have a list of actions - M13
- [ ] 1.1.14. Action is a User Action - M14
- [ ] 1.1.15. Action have a list of resources - M15
- [ ] 1.1.16. Resource is a User Resourc...

## 2. Appointment System Integration

### Sprint Overview

**Sprint Goal:** Integrate the 4 appointment systems so appointments booked through the client-facing planner are immediately visible in the salon management calendar.

**Sprint Duration:** 3 weeks (15 working days)
**Team Capacity:** 4 developers, 1 DevOps engineer
**Story Points Commitment:** 89 points

### Business Context

Currently, appointments booked through the public planner system (appointment-planner-*) are isolated from the internal salon management system (appointment-management-*). This creates operational blind spots where salon staff cannot see client appointments booked online, leading to:

- Double bookings
- Poor customer experience
- Manual workarounds
- Disconnected appointment data

Sprint 3 will establish bidirectional data flow between systems, enabling:
- Real-time appointment visibility across all systems
- Unified appointment status management
- Consistent customer experience
- Operational efficiency for salon staff

### Current System Analysis

#### System Architecture Overview
```
┌─────────────────────┐    ┌─────────────────────┐
│   Planner Frontend  │    │   Management Frontend │
│   (Client-facing)   │    │   (Staff-facing)    │
└──────────┬──────────┘    └──────────┬──────────┘
           │                          │
           │ POST /api/v1/appointments │ GET /api/appointments
           ▼                          ▼
┌─────────────────────┐    ┌─────────────────────┐
│  Planner Backend    │    │ Management Backend  │
│  (Port 4000)        │    │  (Port 4000)         │
│  ┌─────────────────┐│    │  ┌─────────────────┐│
│  │ planner DB ││    │  │ appointments DB ││
│  │ (customer data) ││    │  │ (staff data)    ││
│  └─────────────────┘│    │  └─────────────────┘│
└─────────────────────┘    └─────────────────────┘
       (ISOLATED)                 (ISOLATED)
```
### Data Model Conflicts

**Planner System (Externa...
```