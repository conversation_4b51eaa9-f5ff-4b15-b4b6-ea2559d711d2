```markdown
# 1. Documentation Standards

## 1.1 Service: Documentation
## 1.2 Component: Knowledge Base

Tech stack:
- Typescript
- Node.js
- Vite
- Vitest
- Tailwind CSS

## 1.1 Tailwind CSS v4 Standard

- [ ] 1.1.1. Use Tailwind CSS v4.x only (Not v3.x).
- [ ] 1.1.2. Ensure proper PostCSS integration with the official `@tailwindcss/postcss` plugin.
- [ ] 1.1.3. Follow Tailwind v4 best practices for configuration and merging.
- [ ] 1.1.4. Utilize CSS-first configuration with `@theme` directive for themes and customizations.
- [ ] 1.1.5. Use `@import "tailwindcss"` instead of the old `@tailwind` directives.
- [ ] 1.1.6. Use CSS `@layer` for custom components and utilities.
- [ ] 1.1.7. Leverage CSS variables for design tokens (e.g., `--color-blue-500`).
- [ ] 1.1.8. Utilize built-in utilities like `grid-cols-12`, `z-40`, and `opacity-70`.
- [ ] 1.1.9. Use only v4 utilities, variants, and features.

## 1.2 Rule Name: tailwind_v4_only

## 1.2.1 Description: Enforce the use of Tailwind CSS v4.x in all projects. Do not use Tailwind v3.x. Ensure correct PostCSS integration and follow v4 best practices for configuration and merging.

## 1.2.2 Rule Content

Always use Tailwind CSS v4.x.

Do not use Tailwind CSS v3.x or earlier in any code, documentation, or configuration.

All code examples and configuration must follow Tailwind v4 syntax and features.

## 1.3 How to Use Tailwind CSS v4 Properly

## 1.3.1 Configuration:

Use CSS-first configuration.

Configure themes and customizations directly in your CSS file with the `@theme` directive.

## 1.3.2 Example:

```css
@import "tailwindcss";
@theme {
  --font-display: "Satoshi", "sans-serif";
  --color-primary-500: oklch(0.84 0.18 117.33);
}
```

Do not use `tailwind.config.js` for new features unless specifically required. If you must import legacy config, use `@config` in your CSS:

```css
@import "tailwindcss";
@config "../../tailwind.config.js";
```

Use `@import "tailwindcss"` instead of the old `@tailwind` directives: `@tailwind base; @tailwind components; @tailwind utilities;`.

## 1.3.3 PostCSS Integration:

Use the official `@tailwindcss/postcss` plugin.

Do not use the old `tailwindcss` PostCSS plugin.

No need for `postcss-import` or `autoprefixer`—Tailwind v4 handles these internally.

## 1.3.4 Example `postcss.config.js`:

```js
export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
};
```

## 1.3.5 Merging and Customization:

Use CSS `@layer` for custom components and utilities:

```css
@layer components {
  .card {
    @apply m-10 rounded-lg bg-white;
  }
}
```

All design tokens (colors, fonts, spacing, etc.) are now available as CSS variables (e.g., `var(--color-blue-500)`).

Utilities like `grid-cols-12`, `z-40`, and `opacity-70` work out of the box—no need for extra config.

## 1.4 How to Use Tailwind v4 (Best Practices)

Use only v4 utilities, variants, and features.

Us...
```