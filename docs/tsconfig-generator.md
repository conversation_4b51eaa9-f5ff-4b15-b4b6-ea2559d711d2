# 1. TypeScript Configuration Generator Roadmap

Fully use Clean Architecture and DDD

avoid use prisma in domain layer

Tech stack:
- Typescript
- Node.js
- Vite
- Vitest
- Tailwind CSS

## 1.1 TaaS
Tenant as a Service

- [ ] 1.1.1. Create Tenant (or user pool) - M1
- [ ] 1.1.2. Delete Tenant (or user pool) - M2
- [ ] 1.1.3. Get Tenant (or user pool) - M3
- [ ] 1.1.4. Update Tenant (or user pool) - M4
- [ ] 1.1.5. Get Tenants (or user pools) - M5
- [ ] 1.1.6. Update Tenants (or user pools) - M6
- [ ] 1.1.7. Delete Tenants (or user pools) - M7
- [ ] 1.1.8. Tenant is a User Pool - M8
- [ ] 1.1.9. Tenant have a list of roles - M9
- [ ] 1.1.10. Role is a User Group - M10
- [ ] 1.1.11. Role have a list of policies - M11
- [ ] 1.1.12. Policy is a User Policy - M12
- [ ] 1.1.13. Policy have a list of actions - M13
- [ ] 1.1.14. Action is a User Action - M14
- [ ] 1.1.15. Action have a list of resources - M15
- [ ] 1.1.16. Resource is a User Resourc...

## 1.2 TypeScript Configuration Generator

### 1.2.1 Overview

The TypeScript Configuration Generator is a tool designed to automatically generate and maintain consistent TypeScript configurations across a monorepo with multiple packages. It supports a DDD (Domain-Driven Design) architecture with platform engineering, product engineering, and DDD layers.

### 1.2.2 Architecture

#### ******* Directory Structure

```
root/
├── shared-platform-engineering/    # Platform-level packages
├── shared-product-engineering/     # Product-level packages
└── shared-ddd-layers/             # DDD architectural layers
```

#### ******* Core Components

1. **Generator**
   - Location: `shared-platform-engineering/shared-tsconfig-generator/`
   - Purpose: Generates TypeScript configurations for all packages
   - Entry Point: `bin/generate-tsconfig.ts`

2. **CLI Integration**
   - Package: `computing-lifecycle`
   - Command: `generate -t` or `generate --tsconfig`

### 1.2.3 Implementation Guide

#### ******* Package Structure

```typescript
// Package structure for shared-tsconfig-generator
{
  "name": "@beauty-crm/platform-tsconfig-generator",
  "version": "1.0.0",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "bin": {
    "generate-tsconfig": "./bin/generate-tsconfig.js"
  },
  "scripts": {
    "build": "tsc --build",
    "clean": "rimraf dist",
    "generate": "tsx ./bin/generate-tsconfig.ts"
  }
}
```

#### ******* Core Types

```typescript
// Types for configuration
interface TSConfigReference {
  path: string;
}

interface TSConfigOptions {
  compilerOptions: {
    target: string;
    module: string;
    moduleResolution: string;
    esModuleInterop: boolean;
    strict: boolean;
    skipLibCheck: boolean;
    forceConsistentCasingInFileNames: boolean;
    declaration: boolean;
    outDir?: string;
    rootDir?: string;
    [key: string]: unknown;
  };
  include: string[];
  exclude: string[];
  references?: TSConfigReference[];
}

interface GeneratorConfig {
  baseConfig: TSConfigOptions;
  workspaceRoot: string;
}
```