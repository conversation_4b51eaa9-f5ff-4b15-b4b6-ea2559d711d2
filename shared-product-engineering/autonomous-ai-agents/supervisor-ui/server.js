const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const redis = require('redis');
const axios = require('axios');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Redis client for caching and pub/sub
const redisClient = redis.createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379'
});

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Store for pending approvals and agent activities
const pendingApprovals = new Map();
const agentActivities = new Map();
const confidenceThreshold = parseFloat(process.env.CONFIDENCE_THRESHOLD) || 0.1;

// Initialize Redis connection
redisClient.connect().catch(console.error);

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Get all pending approvals
app.get('/api/approvals', (req, res) => {
  const approvals = Array.from(pendingApprovals.values());
  res.json({ success: true, data: approvals });
});

// Approve or reject an agent action
app.post('/api/approvals/:id/decision', async (req, res) => {
  const { id } = req.params;
  const { decision, feedback } = req.body; // decision: 'approve' | 'reject'

  const approval = pendingApprovals.get(id);
  if (!approval) {
    return res.status(404).json({ success: false, error: 'Approval not found' });
  }

  try {
    // Update approval status
    approval.status = decision;
    approval.feedback = feedback;
    approval.decidedAt = new Date().toISOString();
    approval.decidedBy = req.user?.id || 'supervisor';

    // Notify n8n workflow
    await axios.post(`${process.env.N8N_API_URL}/webhook/approval-decision`, {
      approvalId: id,
      decision,
      feedback,
      originalRequest: approval
    });

    // Remove from pending
    pendingApprovals.delete(id);

    // Broadcast to all connected clients
    io.emit('approval-decided', { id, decision, feedback });

    res.json({ success: true, message: `Action ${decision}d successfully` });
  } catch (error) {
    console.error('Error processing approval decision:', error);
    res.status(500).json({ success: false, error: 'Failed to process decision' });
  }
});

// Get agent activities and status
app.get('/api/agents', (req, res) => {
  const agents = Array.from(agentActivities.values());
  res.json({ success: true, data: agents });
});

// Emergency stop all agents
app.post('/api/emergency-stop', async (req, res) => {
  try {
    // Notify all agents to stop
    await axios.post(`${process.env.N8N_API_URL}/webhook/emergency-stop`, {
      timestamp: new Date().toISOString(),
      reason: req.body.reason || 'Manual emergency stop'
    });

    // Clear all pending approvals
    pendingApprovals.clear();

    // Broadcast emergency stop
    io.emit('emergency-stop', { reason: req.body.reason });

    res.json({ success: true, message: 'Emergency stop initiated' });
  } catch (error) {
    console.error('Error during emergency stop:', error);
    res.status(500).json({ success: false, error: 'Failed to initiate emergency stop' });
  }
});

// Webhook endpoint for n8n to request approvals
app.post('/webhook/request-approval', (req, res) => {
  const {
    agentId,
    action,
    description,
    confidence,
    affectedFiles,
    riskLevel,
    estimatedImpact
  } = req.body;

  // Generate unique approval ID
  const approvalId = `approval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Create approval request
  const approval = {
    id: approvalId,
    agentId,
    action,
    description,
    confidence: parseFloat(confidence),
    affectedFiles: affectedFiles || [],
    riskLevel: riskLevel || 'medium',
    estimatedImpact,
    status: 'pending',
    createdAt: new Date().toISOString(),
    requiresApproval: confidence < confidenceThreshold || riskLevel === 'high'
  };

  // Store approval request
  pendingApprovals.set(approvalId, approval);

  // Broadcast to all connected supervisors
  io.emit('new-approval-request', approval);

  // Log for audit
  console.log(`New approval request: ${approvalId} - ${action} (confidence: ${confidence})`);

  res.json({
    success: true,
    approvalId,
    requiresApproval: approval.requiresApproval
  });
});

// Webhook endpoint for agent status updates
app.post('/webhook/agent-status', (req, res) => {
  const { agentId, status, currentTask, progress, lastActivity } = req.body;

  const agent = {
    id: agentId,
    status,
    currentTask,
    progress: parseFloat(progress) || 0,
    lastActivity: lastActivity || new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  agentActivities.set(agentId, agent);

  // Broadcast agent status update
  io.emit('agent-status-update', agent);

  res.json({ success: true });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('Supervisor connected:', socket.id);

  // Send current state to new connection
  socket.emit('initial-state', {
    pendingApprovals: Array.from(pendingApprovals.values()),
    agentActivities: Array.from(agentActivities.values())
  });

  socket.on('disconnect', () => {
    console.log('Supervisor disconnected:', socket.id);
  });
});

// Error handling
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({ success: false, error: 'Internal server error' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    pendingApprovals: pendingApprovals.size,
    activeAgents: agentActivities.size
  });
});

// Start server
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`AI Agent Supervisor running on port ${PORT}`);
  console.log(`Confidence threshold: ${confidenceThreshold}`);
  console.log(`Human approval required: ${process.env.HUMAN_APPROVAL_REQUIRED !== 'false'}`);
});
