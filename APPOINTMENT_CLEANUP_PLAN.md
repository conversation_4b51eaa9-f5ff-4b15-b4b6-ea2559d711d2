```markdown
# 1. Authentication & User Management Roadmap

Fully use Clean Architecture and DDD

avoid use prisma in domain layer

Tech stack:
- Typescript
- Node.js
- React
- Clean Architecture and DDD
- Vite
- Vitest
- Tailwind CSS

## 1.1 TaaS
Tenant as a Service

- [ ] 1.1.1. Create Tenant (or user pool) - M1
- [ ] 1.1.2. Delete Tenant (or user pool) - M2
- [ ] 1.1.3. Get Tenant (or user pool) - M3
- [ ] 1.1.4. Update Tenant (or user pool) - M4
- [ ] 1.1.5. Get Tenants (or user pools) - M5
- [ ] 1.1.6. Update Tenants (or user pools) - M6
- [ ] 1.1.7. Delete Tenants (or user pools) - M7
- [ ] 1.1.8. Tenant is a User Pool - M8
- [ ] 1.1.9. Tenant have a list of roles - M9
- [ ] 1.1.10. Role is a User Group - M10
- [ ] 1.1.11. Role have a list of policies - M11
- [ ] 1.1.12. Policy is a User Policy - M12
- [ ] 1.1.13. Policy have a list of actions - M13
- [ ] 1.1.14. Action is a User Action - M14
- [ ] 1.1.15. Action have a list of resources - M15
- [ ] 1.1.16. Resource is a User Resourc...

## 2. Appointment Schema Duplication Analysis

### 2.1 Duplicate Appointment Models
- [ ] `services/appointment/appointment-management-backend/src/domain/models/AppointmentModel.ts` - 150+ lines
- [ ] `services/appointment/appointment-planner-backend/src/domain/models/appointment.ts` - 200+ lines
- [ ] `services/appointment/appointment-management-frontend/src/product-features/appointments/appointment/AppointmentForm.tsx` - Appointment type
- [ ] `services/appointment/appointment-management-frontend/src/services/appointment-service.ts` - Appointment interface
- [ ] `shared-product-engineering/product-appointment-types/src/appointment/types.ts` - 300+ lines
- [ ] `shared-product-engineering/product-appointment-types/src/appointment/unified-types.ts` - UnifiedAppointment
- [ ] `shared-ddd-layers/domain/src/events/appointment-events.ts` - AppointmentEventData

### 2.2 Duplicate Zod Schemas
- [ ] `services/appointment/appointment-management-backend/src/shared/types/appointment-events.ts` - AppointmentSchema
- [ ] `services/appointment/appointment-management-backend/src/controllers/AppointmentController.ts` - appointmentSchema
- [ ] `services/appointment/appointment-planner-backend/src/presentation/controllers/appointmentController.ts` - createAppointmentSchema
- [ ] `services/appointment/appointment-planner-backend/src/domain/commands/AppointmentCommands.ts` - createAppointmentCommandSchema
- [ ] `services/appointment/appointment-planner-backend/src/domain/events/AppointmentEvents.ts` - appointmentCreatedEventSchema
- [ ] `shared-ddd-layers/domain/src/events/appointment-events.ts` - AppointmentEventSchema

### 2.3 Duplicate Prisma Schemas
- [ ] `services/appointment/appointment-management-backend/prisma/schema.prisma` - Appointment model
- [ ] `services/appointment/appointment-planner-backend/prisma/schema.prisma` - Appointment model
- [ ] `services/appointme...
```