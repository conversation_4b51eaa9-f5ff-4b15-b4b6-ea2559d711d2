#!/usr/bin/env node

/**
 * Local SDLC Workflow Importer
 * Imports local file-based SDLC agents into n8n
 */

const fs = require('fs');
const path = require('path');
const http = require('http');

// Configuration
const N8N_BASE_URL = 'http://localhost:5678';
const WORKFLOWS_DIR = path.join(__dirname, 'workflows');
const MAX_RETRIES = 10;
const RETRY_DELAY = 3000;

// Colors
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  purple: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = http.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = data ? JSON.parse(data) : {};
          resolve({ statusCode: res.statusCode, data: jsonData });
        } catch (error) {
          resolve({ statusCode: res.statusCode, data: data });
        }
      });
    });

    req.on('error', reject);

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

async function waitForN8n() {
  log('⏳ Waiting for n8n to be ready...', 'yellow');

  for (let i = 0; i < MAX_RETRIES; i++) {
    try {
      const response = await makeRequest(`${N8N_BASE_URL}/healthz`);
      if (response.statusCode === 200) {
        log('✅ n8n is ready!', 'green');
        return true;
      }
    } catch (error) {
      // n8n not ready yet
    }

    if (i < MAX_RETRIES - 1) {
      log(`   Attempt ${i + 1}/${MAX_RETRIES} failed, retrying in ${RETRY_DELAY / 1000}s...`, 'yellow');
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
    }
  }

  throw new Error('n8n failed to become ready');
}

async function getExistingWorkflows() {
  try {
    const response = await makeRequest(`${N8N_BASE_URL}/api/v1/workflows`);
    if (response.statusCode === 200) {
      return response.data.data || [];
    }
    return [];
  } catch (error) {
    log(`⚠️ Could not fetch existing workflows: ${error.message}`, 'yellow');
    return [];
  }
}

async function importWorkflow(workflowFile) {
  const workflowPath = path.join(WORKFLOWS_DIR, workflowFile);

  try {
    const workflowData = JSON.parse(fs.readFileSync(workflowPath, 'utf8'));
    const workflowName = workflowData.name;

    log(`📥 Importing: ${workflowName}`, 'blue');

    // Check if workflow already exists
    const existingWorkflows = await getExistingWorkflows();
    const existingWorkflow = existingWorkflows.find(w => w.name === workflowName);

    if (existingWorkflow) {
      log(`   🔄 Updating existing workflow (ID: ${existingWorkflow.id})`, 'yellow');

      const updateResponse = await makeRequest(`${N8N_BASE_URL}/api/v1/workflows/${existingWorkflow.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(workflowData)
      });

      if (updateResponse.statusCode === 200) {
        log(`   ✅ Updated: ${workflowName}`, 'green');
        return { action: 'updated', workflow: updateResponse.data };
      } else {
        log(`   ❌ Update failed: ${updateResponse.statusCode}`, 'red');
        return { action: 'failed', error: updateResponse.data };
      }
    } else {
      const createResponse = await makeRequest(`${N8N_BASE_URL}/api/v1/workflows`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(workflowData)
      });

      if (createResponse.statusCode === 201 || createResponse.statusCode === 200) {
        log(`   ✅ Created: ${workflowName} (ID: ${createResponse.data.id})`, 'green');
        return { action: 'created', workflow: createResponse.data };
      } else {
        log(`   ❌ Creation failed: ${createResponse.statusCode}`, 'red');
        return { action: 'failed', error: createResponse.data };
      }
    }
  } catch (error) {
    log(`   ❌ Error importing ${workflowFile}: ${error.message}`, 'red');
    return { action: 'failed', error: error.message };
  }
}

async function activateWorkflow(workflowId, workflowName) {
  try {
    const response = await makeRequest(`${N8N_BASE_URL}/api/v1/workflows/${workflowId}/activate`, {
      method: 'POST'
    });

    if (response.statusCode === 200) {
      log(`   ⚡ Activated: ${workflowName}`, 'green');
      return true;
    } else {
      log(`   ⚠️ Activation failed: ${workflowName}`, 'yellow');
      return false;
    }
  } catch (error) {
    log(`   ⚠️ Activation error: ${workflowName}`, 'yellow');
    return false;
  }
}

async function main() {
  log('🤖 Local SDLC Workflow Importer', 'purple');
  log('=================================', 'purple');

  try {
    await waitForN8n();

    if (!fs.existsSync(WORKFLOWS_DIR)) {
      log(`❌ Workflows directory not found: ${WORKFLOWS_DIR}`, 'red');
      process.exit(1);
    }

    // Get local workflow files (prioritize local-* files)
    const allFiles = fs.readdirSync(WORKFLOWS_DIR).filter(file => file.endsWith('.json'));
    const localFiles = allFiles.filter(file => file.startsWith('local-')).sort();
    const otherFiles = allFiles.filter(file => !file.startsWith('local-')).sort();
    const workflowFiles = [...localFiles, ...otherFiles];

    if (workflowFiles.length === 0) {
      log('⚠️ No workflow files found', 'yellow');
      process.exit(0);
    }

    log(`📁 Found ${workflowFiles.length} workflow files (${localFiles.length} local agents)`, 'blue');

    const results = {
      created: 0,
      updated: 0,
      failed: 0,
      activated: 0
    };

    for (const workflowFile of workflowFiles) {
      const result = await importWorkflow(workflowFile);

      if (result.action === 'created') {
        results.created++;
        if (await activateWorkflow(result.workflow.id, result.workflow.name)) {
          results.activated++;
        }
      } else if (result.action === 'updated') {
        results.updated++;
        if (await activateWorkflow(result.workflow.id, result.workflow.name)) {
          results.activated++;
        }
      } else {
        results.failed++;
      }

      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    log('', 'reset');
    log('🎉 Local SDLC Workflow Import Complete!', 'green');
    log('========================================', 'green');
    log(`📊 Results:`, 'blue');
    log(`   ✅ Created: ${results.created}`, 'green');
    log(`   🔄 Updated: ${results.updated}`, 'yellow');
    log(`   ❌ Failed: ${results.failed}`, 'red');
    log(`   ⚡ Activated: ${results.activated}`, 'green');
    log('', 'reset');

    if (results.activated > 0) {
      log('🚀 Local SDLC Agents are now running!', 'purple');
      log('📊 Monitor at: http://localhost:5678/executions', 'cyan');
      log('🔧 Edit workflows: http://localhost:5678/workflows', 'cyan');
      log('', 'reset');
      log('💡 The agents will:', 'blue');
      log('   1. 🔍 Scan local files every 5 minutes for tasks', 'cyan');
      log('   2. 🧠 Analyze tasks with Gemini AI', 'cyan');
      log('   3. 📝 Make REAL file changes in /workspace', 'cyan');
      log('   4. ✅ Run TypeScript/ESLint quality checks', 'cyan');
      log('   5. 📊 Create actual git commits', 'cyan');
      log('   6. 🔍 Monitor files every 2 minutes for issues', 'cyan');
      log('   7. 🧹 Auto-fix problems and cleanup old files', 'cyan');
      log('   8. 🚀 Optimize system performance continuously', 'cyan');
    }

    process.exit(results.failed > 0 ? 1 : 0);

  } catch (error) {
    log(`❌ Fatal error: ${error.message}`, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { importWorkflow, waitForN8n }; "
      },
"id": "import-script",
  "name": "Import Local Workflows",
    "type": "n8n-nodes-base.code",
      "typeVersion": 2,
        "position": [100, 300]
    }
  ],
"connections": { },
"active": false,
  "settings": { },
"versionId": "1",
  "meta": {
  "templateCredsSetupCompleted": true
},
"id": "import-local-workflows"
}
