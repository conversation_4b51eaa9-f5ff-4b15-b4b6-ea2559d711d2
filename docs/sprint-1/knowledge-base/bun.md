```markdown
# 1. Documentation Standards Roadmap

Fully use Clean Architecture and DDD

avoid use prisma in domain layer

Tech stack:
- Typescript
- Node.js
- Vite
- Vitest
- Tailwind CSS

## 1.1 Knowledge Base

- [ ] 1.1.1. Bun Library Best Practices - M1
- [ ] 1.1.2. Authentication & User Management Roadmap - M2
- [ ] 1.1.3. API Documentation Standards - M3
- [ ] 1.1.4. Database Schema Documentation - M4
- [ ] 1.1.5. Testing Strategy Documentation - M5
- [ ] 1.1.6. Deployment Process Documentation - M6
- [ ] 1.1.7. Code Style Guide - M7
- [ ] 1.1.8. Error Handling Guidelines - M8
- [ ] 1.1.9. Security Best Practices - M9
- [ ] 1.1.10. Monitoring and Logging - M10
- [ ] 1.1.11. CI/CD Pipeline Documentation - M11
- [ ] 1.1.12. Infrastructure as Code (IaC) - M12
- [ ] 1.1.13. Data Model Documentation - M13
- [ ] 1.1.14. API Endpoint Documentation - M14
- [ ] 1.1.15. Component Documentation - M15

## 2. Bun Library Best Practices

This document outlines the recommended coding standards, best practices, and patterns for developing applications using the Bun library. Following these guidelines ensures maintainability, performance, security, and overall code quality.

## 2.1. Code Organization and Structure

### 2.1.1. Directory Structure

A well-defined directory structure is crucial for maintainability. Consider the following structure as a starting point:

```
project-root/
├── src/                  # Source code
│   ├── components/       # Reusable UI components (if applicable)
│   ├── services/         # Business logic and API interactions
│   ├── utils/            # Utility functions
│   ├── types/            # TypeScript type definitions
│   ├── routes/           # API route handlers
│   ├── middleware/       # Middleware functions
│   ├── models/           # Data models
│   ├── config/           # Configuration files
│   ├── index.ts          # Entry point for the application
├── tests/                # Unit and integration tests
├── public/               # Static assets (e.g., images, CSS)
├── .env                  # Environment variables
├── bun.lockb             # Lockfile for dependencies
├── package.json          # Project metadata and dependencies
├── tsconfig.json         # TypeScript configuration
├── README.md             # Project documentation
```

*   **src/**: Contains the main source code of the application.
*   **components/**: Houses reusable UI components (if building a web application).
*   **services/**: Encapsulates business logic and interactions with external APIs.
*   **utils/**: Contains utility functions.

## 3. Authentication & User Management Roadmap

- [ ] 3.1.1. Create Tenant (or user pool) - M1
- [ ] 3.1.2. Delete Tenant (or user pool) - M2
- [ ] 3.1.3. Get Tenant (or user pool) - M3
- [ ] 3.1.4. Update Tenant (or user pool) - M4
- [ ] 3.1.5. Get Tenants (or user pools) - M5
- [ ] 3.1.6. Update Tenants (or user pools) - M6
- [ ] 3.1.7. Delete Tenants (or user pools) - M7
- [ ] 3.1.8. Tenant is a User Pool - M8
- [ ] 3.1.9. Tenant have a list of roles - M9
- [ ] 3.1.10. Role is a User Group - M10
- [ ] 3.1.11. Role have a list of policies - M11
- [ ] 3.1.12. Policy is a User Policy - M12
- [ ] 3.1.13. Policy have a list of actions - M13
- [ ] 3.1.14. Action is a User Action - M14
- [ ] 3.1.15. Action have a list of resources - M15
- [ ] 3.1.16. Resource is a User Resourc...
```