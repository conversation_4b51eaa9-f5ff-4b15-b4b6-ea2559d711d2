```markdown
# 1. Documentation

## 1.1 Knowledge Base

- [ ] 1.1.1. Create Guide - M1
- [ ] 1.1.2. Update Guide - M2
- [ ] 1.1.3. Delete Guide - M3
- [ ] 1.1.4. Get Guide - M4
- [ ] 1.1.5. Search Guide - M5

## 1.2 Dev Workflow

Taskmaster Development Workflow

This guide outlines the standard process for using Taskmaster to manage software development projects. It is written as a set of instructions for you, the AI agent.

**Your Default Stance**: For most projects, the user can work directly within the `master` task context. Your initial actions should operate on this default context unless a clear pattern for multi-context work emerges.

**Your Goal**: Your role is to elevate the user's workflow by intelligently introducing advanced features like **Tagged Task Lists** when you detect the appropriate context. Do not force tags on the user; suggest them as a helpful solution to a specific need.

## 1. Basic Loop

1.  **`list`**: Show the user what needs to be done.
2.  **`next`**: Help the user decide what to work on.
3.  **`show <id>`**: Provide details for a specific task.
4.  **`expand <id>`**: Break down a complex task into smaller, manageable subtasks.
5.  **Implement**: The user writes the code and tests.
6.  **`update-subtask`**: Log progress and findings on behalf of the user.
7.  **`set-status`**: Mark tasks and subtasks as `done` as work is completed.
8.  **Repeat**.

All your standard command executions should operate on the user's current task context, which defaults to `master`.

## 2. Standard Development Workflow Process

### 2.1 Simple Workflow (Default Starting Point)

For new projects or when users are getting started, operate within the `master` tag context:

-   Start new projects by running `initialize_project` tool / `task-master init` or `parse_prd` / `task-master parse-prd --input='<prd-file.txt>'` (see @`taskmaster.md`) to generate initial tasks.json with tagged structure
-   Configure rule sets during initialization with `--rules` flag (e.g., `task-master init --rules trae,windsurf`).

## 3. Tech Stack

- Typescript
- Node.js
- Prisma
- React
- Clean Architecture and DDD
- Vite
- Vitest
- Tailwind CSS
```