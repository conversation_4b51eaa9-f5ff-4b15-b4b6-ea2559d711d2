# 1. Authentication & User Management Roadmap

Fully use Clean Architecture and DDD

avoid use prisma in domain layer

Tech stack:
- Typescript
- Node.js
- Vite
- Vitest
- Tailwind CSS

## 1.1 TaaS
Tenant as a Service

- [ ] 1.1.1. Create Tenant (or user pool) - M1
- [ ] 1.1.2. Delete Tenant (or user pool) - M2
- [ ] 1.1.3. Get Tenant (or user pool) - M3
- [ ] 1.1.4. Update Tenant (or user pool) - M4
- [ ] 1.1.5. Get Tenants (or user pools) - M5
- [ ] 1.1.6. Update Tenants (or user pools) - M6
- [ ] 1.1.7. Delete Tenants (or user pools) - M7
- [ ] 1.1.8. Tenant is a User Pool - M8
- [ ] 1.1.9. Tenant have a list of roles - M9
- [ ] 1.1.10. Role is a User Group - M10
- [ ] 1.1.11. Role have a list of policies - M11
- [ ] 1.1.12. Policy is a User Policy - M12
- [ ] 1.1.13. Policy have a list of actions - M13
- [ ] 1.1.14. Action is a User Action - M14
- [ ] 1.1.15. Action have a list of resources - M15
- [ ] 1.1.16. Resource is a User Resourc...

## 2. Appointment Creation - Sprint 1

**Sprint Goal:** Implement the complete, end-to-end flow for creating a new appointment, ensuring it is robust, transactional, and fully integrated with the eventing system across all relevant services.

This sprint focuses on building a solid foundation for the appointment domain by implementing the core creation logic according to the principles of Clean Architecture and Event-Driven Design.

## 2.1 Architecture Overview

The implementation will adhere to the Ports and Adapters (Hexagonal) architecture, using a consistent set of interfaces for each service. The core flow involves:
1.  **API Layer (`presentation`):** Receives external requests (e.g., HTTP).
2.  **Application Layer (`application`):** A use case service orchestrates the business logic.
3.  **Domain Layer (`domain`):** Contains the core business logic, aggregates, and ports.

## 2.2 Integration Tasks

### 2.2.1 Integrate `platform-appointment-eventing` into Backend Services

Both the following services must be updated to use the new event publisher abstraction:
- `services/appointment/appointment-planner-backend`
- `services/appointment/appointment-management-backend`

#### ******* Migration Steps:

- [ ] *******.1 **Add Dependency:** Ensure both services have `platform-appointment-eventing` as a dependency in their package.json.
- [ ] *******.2 **Refactor Event Publishing:** Replace any direct usage of NATS or legacy event publisher code with the new `AppointmentEventPublisherImpl` from `platform-appointment-eventing`. Use the provided factory or class to publish all appointment-related events (upsert, cancel, etc.). Remove direct NATS connection logic and related configuration from these services.
- [ ] *******.3 **Update Event Payloads:** Ensure all published events conform to the new `DomainEvent` schema and use the correct data structures (e.g., `UnifiedAppointment`). Update any event consumers to expect the new schema if necessary.
- [ ] *******.4 **Testing:** ...