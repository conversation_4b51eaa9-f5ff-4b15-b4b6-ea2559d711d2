#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const WORKSPACE_ROOT = '/private/var/www/2025/ollamar1/beauty-crm';

// Find all legitimate markdown files
function findMarkdownFiles() {
    const command = `find ${WORKSPACE_ROOT} -name "*.md" -type f \\
        -not -path "*/node_modules/*" \\
        -not -path "*/.*/*" \\
        -not -path "*/dist/*" \\
        -not -path "*/build/*" \\
        -not -path "*/coverage/*" \\
        -not -path "*/tmp/*" \\
        -not -path "*/temp/*" \\
        -not -path "*/logs/*" \\
        -not -path "*/log/*" \\
        -not -path "*/target/*" \\
        -not -path "*/out/*" \\
        -not -path "*/bin/*" \\
        -not -path "*/obj/*" \\
        -not -path "*/shared-product-engineering/autonomous-ai-agents/*"`;
    
    try {
        const output = execSync(command, { encoding: 'utf8' });
        return output.trim().split('\n').filter(file => file.length > 0);
    } catch (error) {
        console.error('Error finding markdown files:', error.message);
        return [];
    }
}

// Extract existing tasks from content
function extractTasks(content) {
    const tasks = [];
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
        // Look for various task patterns
        const patterns = [
            /^[\s]*[-*+]?\s*\[[ x]\]\s+(.+)$/i,  // Checkbox items
            /^[\s]*[-*+]?\s*TODO:?\s+(.+)$/i,    // TODO items
            /^[\s]*[-*+]?\s*FIXME:?\s+(.+)$/i,   // FIXME items
            /^[\s]*[-*+]?\s*Action:?\s+(.+)$/i,  // Action items
            /^[\s]*[-*+]?\s*Need to:?\s+(.+)$/i, // Need to items
            /^[\s]*[-*+]?\s*Should:?\s+(.+)$/i,  // Should items
            /^[\s]*[-*+]?\s*Must:?\s+(.+)$/i,    // Must items
            /^[\s]*[-*+]\s+(.+)$/                // General bullet points
        ];
        
        patterns.forEach(pattern => {
            const match = line.match(pattern);
            if (match && match[1] && match[1].trim().length > 10) {
                const taskText = match[1].trim();
                const isCompleted = line.includes('[x]') || line.includes('[X]');
                tasks.push({
                    text: taskText,
                    completed: isCompleted,
                    lineNumber: index + 1
                });
            }
        });
    });
    
    return tasks;
}

// Add task section to file
function addTaskSection(filePath, content) {
    const relativePath = path.relative(WORKSPACE_ROOT, filePath);
    const fileName = path.basename(filePath, '.md');
    
    // Extract existing tasks
    const existingTasks = extractTasks(content);
    
    // Skip if file already has a task section or is very small
    if (content.includes('## Tasks') || content.includes('## TODO') || content.length < 100) {
        return null;
    }
    
    // Create task section
    let taskSection = '\n\n## Tasks\n\n';
    
    if (existingTasks.length > 0) {
        taskSection += '### Extracted Tasks\n\n';
        existingTasks.forEach((task, index) => {
            const checkbox = task.completed ? '[x]' : '[ ]';
            taskSection += `- ${checkbox} ${task.text} - M${index + 1}\n`;
        });
        taskSection += '\n';
    }
    
    // Add common task categories based on file type/location
    if (relativePath.includes('backend') || relativePath.includes('api')) {
        taskSection += '### Backend Tasks\n\n';
        taskSection += '- [ ] Implement API endpoints - M1\n';
        taskSection += '- [ ] Add input validation - M2\n';
        taskSection += '- [ ] Add error handling - M3\n';
        taskSection += '- [ ] Add unit tests - M4\n';
        taskSection += '- [ ] Add integration tests - M5\n';
        taskSection += '- [ ] Add documentation - M6\n\n';
    } else if (relativePath.includes('frontend') || relativePath.includes('ui')) {
        taskSection += '### Frontend Tasks\n\n';
        taskSection += '- [ ] Implement UI components - M1\n';
        taskSection += '- [ ] Add form validation - M2\n';
        taskSection += '- [ ] Add error handling - M3\n';
        taskSection += '- [ ] Add loading states - M4\n';
        taskSection += '- [ ] Add unit tests - M5\n';
        taskSection += '- [ ] Add accessibility features - M6\n\n';
    } else if (relativePath.includes('docs') || relativePath.includes('knowledge-base')) {
        taskSection += '### Documentation Tasks\n\n';
        taskSection += '- [ ] Update content accuracy - M1\n';
        taskSection += '- [ ] Add code examples - M2\n';
        taskSection += '- [ ] Add diagrams/screenshots - M3\n';
        taskSection += '- [ ] Review and proofread - M4\n';
        taskSection += '- [ ] Add cross-references - M5\n\n';
    } else {
        taskSection += '### General Tasks\n\n';
        taskSection += '- [ ] Review and update content - M1\n';
        taskSection += '- [ ] Add missing information - M2\n';
        taskSection += '- [ ] Improve structure - M3\n';
        taskSection += '- [ ] Add examples - M4\n';
        taskSection += '- [ ] Validate accuracy - M5\n\n';
    }
    
    // Append task section to original content
    return content + taskSection;
}

// Main function
async function main() {
    console.log('🔍 Finding markdown files...');
    const markdownFiles = findMarkdownFiles();
    console.log(`📋 Found ${markdownFiles.length} markdown files`);
    
    let processed = 0;
    let skipped = 0;
    let errors = 0;
    
    for (const filePath of markdownFiles) {
        try {
            const relativePath = path.relative(WORKSPACE_ROOT, filePath);
            
            // Skip backup files
            if (filePath.endsWith('.backup')) {
                continue;
            }
            
            const originalContent = fs.readFileSync(filePath, 'utf8');
            const updatedContent = addTaskSection(filePath, originalContent);
            
            if (updatedContent && updatedContent !== originalContent) {
                // Create backup
                const backupPath = filePath + '.backup';
                if (!fs.existsSync(backupPath)) {
                    fs.writeFileSync(backupPath, originalContent);
                }
                
                // Write updated content
                fs.writeFileSync(filePath, updatedContent);
                
                console.log(`✅ Added tasks to: ${relativePath}`);
                processed++;
            } else {
                console.log(`⏭️  Skipped: ${relativePath}`);
                skipped++;
            }
        } catch (error) {
            console.error(`❌ Error processing ${filePath}:`, error.message);
            errors++;
        }
    }
    
    console.log('\n📊 TASK SECTION ADDITION COMPLETE');
    console.log(`✅ Processed: ${processed}`);
    console.log(`⏭️  Skipped: ${skipped}`);
    console.log(`❌ Errors: ${errors}`);
    console.log(`📁 Total files: ${markdownFiles.length}`);
    
    // Trigger task discovery scan
    console.log('\n🔍 Triggering task discovery scan...');
    try {
        const response = await fetch('http://localhost:3003/api/tasks/scan', {
            method: 'POST'
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ Task discovery scan completed');
            console.log(`📋 Tasks found: ${result.results?.totalTasks || 'unknown'}`);
            console.log(`📋 New tasks: ${result.results?.newTasks || 'unknown'}`);
        } else {
            console.log('⚠️  Task discovery scan failed - run manually');
        }
    } catch (error) {
        console.log('⚠️  Could not trigger task discovery scan - run manually');
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { findMarkdownFiles, addTaskSection };
