```markdown
# 1. Distributed Systems Reliability Patterns (Onboarding)

All engineers must review and understand the approved reliability patterns before contributing reliability-critical code.

Tech stack:
- Typescript
- Node.js
- ... (Add other relevant technologies)

## 1.1 Reliability Patterns Review

- [ ] Review approved reliability patterns, templates, and code examples in [docs/reliability-patterns.md](./reliability-patterns.md).
- [ ] Explore Microsoft Cloud Design Patterns: [https://learn.microsoft.com/en-us/azure/architecture/patterns/](https://learn.microsoft.com/en-us/azure/architecture/patterns/)
- [ ] Explore AWS Well-Architected Framework: [https://aws.amazon.com/architecture/well-architected/](https://aws.amazon.com/architecture/well-architected/)
- [ ] Review Example PRs: (add links as they are created)
```