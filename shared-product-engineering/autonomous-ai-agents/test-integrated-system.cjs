#!/usr/bin/env node

const axios = require('axios');

// Test the complete integrated system
async function testIntegratedSystem() {
    console.log('🧪 Testing Integrated Markdown Analyzer → SDLC Agents System\n');

    // 1. Check system health
    console.log('🔍 Checking system health...');

    try {
        // Check markdown analyzer
        const analyzerResponse = await axios.get('http://localhost:3003/api/stats');
        console.log(`✅ Markdown Analyzer: ${analyzerResponse.data.stats.total} tasks discovered`);

        // Check supervisor
        const supervisorResponse = await axios.get('http://localhost:3001/api/agents');
        console.log(`✅ Supervisor: ${supervisorResponse.data.data.length} agents registered`);

        // Check n8n (skip auth check, just verify it's running)
        try {
            await axios.get('http://localhost:5678');
            console.log(`✅ n8n: Service is running`);
        } catch (error) {
            console.log(`⚠️  n8n: Service may not be accessible (${error.message})`);
        }

    } catch (error) {
        console.error('❌ System health check failed:', error.message);
        return;
    }

    console.log('\n🚀 System health check passed! Starting integration test...\n');

    // 2. Trigger the markdown analyzer to SDLC agents workflow manually
    console.log('📋 Triggering task assignment workflow...');

    try {
        // First, let's get some tasks from the markdown analyzer
        const tasksResponse = await axios.get('http://localhost:3003/api/tasks?completed=false&priority=low,medium');

        if (!tasksResponse.data.success || !tasksResponse.data.tasks) {
            console.log('⚠️  No tasks available from markdown analyzer');
            return;
        }

        const availableTasks = tasksResponse.data.tasks.slice(0, 5); // Take first 5 tasks
        console.log(`📊 Found ${availableTasks.length} tasks to assign`);

        // 3. Classify and assign tasks to SDLC agents
        const assignments = [];

        for (const task of availableTasks) {
            const taskText = task.text.toLowerCase();

            // Classify task by SDLC specialization
            let agentType = 'documentation-specialist';
            let webhookPath = 'docs-agent';

            if (taskText.includes('ui') || taskText.includes('frontend') || taskText.includes('component') || taskText.includes('react') || taskText.includes('css')) {
                agentType = 'frontend-developer';
                webhookPath = 'frontend-agent';
            } else if (taskText.includes('api') || taskText.includes('backend') || taskText.includes('database') || taskText.includes('server') || taskText.includes('endpoint')) {
                agentType = 'backend-developer';
                webhookPath = 'backend-agent';
            } else if (taskText.includes('test') || taskText.includes('testing') || taskText.includes('qa') || taskText.includes('validation') || taskText.includes('verify')) {
                agentType = 'qa-tester';
                webhookPath = 'qa-agent';
            } else if (taskText.includes('deploy') || taskText.includes('docker') || taskText.includes('ci/cd') || taskText.includes('infrastructure') || taskText.includes('devops')) {
                agentType = 'devops-engineer';
                webhookPath = 'devops-agent';
            } else if (taskText.includes('requirement') || taskText.includes('analysis') || taskText.includes('business') || taskText.includes('stakeholder')) {
                agentType = 'requirements-analyst';
                webhookPath = 'requirements-agent';
            }

            console.log(`🎯 Classifying task: "${task.text.substring(0, 50)}..."`);
            console.log(`   → Assigned to: ${agentType}`);
            console.log(`   → File: ${task.filePath}:${task.lineNumber}`);
            console.log(`   → Priority: ${task.priority} | Complexity: ${task.estimatedComplexity}`);

            // 4. Assign task to the appropriate SDLC agent
            try {
                const assignmentPayload = {
                    task: task.text,
                    description: `Auto-assigned from markdown analysis: ${task.filePath}`,
                    priority: task.priority || 'medium',
                    complexity: task.estimatedComplexity,
                    taskId: task.id,
                    filePath: task.filePath,
                    lineNumber: task.lineNumber,
                    agentType: agentType,
                    tags: task.tags
                };

                const assignmentResponse = await axios.post(`http://localhost:5678/webhook/${webhookPath}`, assignmentPayload);

                if (assignmentResponse.status === 200) {
                    console.log(`   ✅ Successfully assigned to ${agentType}`);
                    assignments.push({
                        task,
                        agentType,
                        webhookPath,
                        success: true,
                        response: assignmentResponse.data
                    });
                } else {
                    console.log(`   ⚠️  Assignment may have failed (status: ${assignmentResponse.status})`);
                    assignments.push({
                        task,
                        agentType,
                        webhookPath,
                        success: false,
                        error: `HTTP ${assignmentResponse.status}`
                    });
                }

            } catch (error) {
                console.log(`   ❌ Assignment failed: ${error.message}`);
                assignments.push({
                    task,
                    agentType,
                    webhookPath,
                    success: false,
                    error: error.message
                });
            }

            console.log(''); // Empty line for readability

            // Small delay between assignments
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // 5. Summary of assignments
        console.log('📊 ASSIGNMENT SUMMARY:');
        const successful = assignments.filter(a => a.success).length;
        const failed = assignments.filter(a => !a.success).length;

        console.log(`✅ Successful assignments: ${successful}`);
        console.log(`❌ Failed assignments: ${failed}`);
        console.log(`📁 Total tasks processed: ${assignments.length}`);

        // 6. Check agent status after assignments
        console.log('\n👥 Checking agent status after assignments...');

        try {
            const agentsResponse = await axios.get('http://localhost:3001/api/agents');
            const agents = agentsResponse.data.data;

            console.log(`📊 Agent Status Summary:`);
            agents.forEach(agent => {
                const status = agent.status === 'working' ? '🔄 WORKING' : '💤 IDLE';
                const grade = agent.grade !== 'N/A' ? `${agent.grade} (${agent.score}/100)` : 'No Grade';
                console.log(`   ${agent.id}: ${status} | Grade: ${grade} | Tasks: ${agent.performance.tasksCompleted}/${agent.performance.tasksAssigned}`);
            });

        } catch (error) {
            console.log('⚠️  Could not fetch agent status:', error.message);
        }

        // 7. Test completion feedback (simulate)
        if (successful > 0) {
            console.log('\n🔄 Testing task completion feedback loop...');

            const completedTask = assignments.find(a => a.success);
            if (completedTask) {
                try {
                    const completionPayload = {
                        taskId: completedTask.task.id,
                        agentId: `agent-${completedTask.agentType}-001`,
                        successful: true,
                        result: 'Task completed successfully via integration test',
                        completedAt: new Date().toISOString()
                    };

                    const completionResponse = await axios.post('http://localhost:5678/webhook/task-completed', completionPayload);

                    if (completionResponse.status === 200) {
                        console.log('✅ Task completion feedback loop working');
                    } else {
                        console.log('⚠️  Task completion feedback may have issues');
                    }

                } catch (error) {
                    console.log('❌ Task completion feedback failed:', error.message);
                }
            }
        }

        console.log('\n🎉 Integration test completed!');
        console.log('\n📊 System Status:');
        console.log('   🔍 Markdown Analyzer: Discovering tasks from README files');
        console.log('   🤖 SDLC Agents: Ready to process assigned tasks');
        console.log('   🎓 Supervisor: Monitoring agent performance with grades');
        console.log('   🔧 n8n Workflows: Orchestrating task assignments');
        console.log('   🔄 Feedback Loop: Updating task completion status');

        console.log('\n🚀 The integrated system is operational!');
        console.log('   📋 Tasks are automatically discovered from markdown files');
        console.log('   🎯 Tasks are intelligently assigned to specialized SDLC agents');
        console.log('   📊 Agent performance is tracked with internship-style grading');
        console.log('   ✅ Task completion updates the original markdown files');

    } catch (error) {
        console.error('❌ Integration test failed:', error.message);
    }
}

// Run the test
if (require.main === module) {
    testIntegratedSystem().catch(console.error);
}

module.exports = { testIntegratedSystem };
