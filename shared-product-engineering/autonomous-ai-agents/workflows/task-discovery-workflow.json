{"name": "Task Discovery and Assignment", "nodes": [{"parameters": {"httpMethod": "POST", "path": "new-task-discovered", "responseMode": "responseNode", "options": {}}, "id": "task-discovery-webhook", "name": "New Task Discovered", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"method": "POST", "url": "http://localhost:11434/api/generate", "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "gemma-3n-e4b-it"}, {"name": "prompt", "value": "=You are an AI task analyzer for the Beauty CRM project.\n\nSystem Instructions:\n- Be extremely conservative with confidence scores\n- Default confidence should be 0.05 unless absolutely certain\n- Analyze tasks for AI agent feasibility\n- Consider complexity and risk factors\n- Provide structured JSON responses only\n\nTask Analysis:\nTask: {{ $json.task }}\nFile: {{ $json.filePath }}\nPriority: {{ $json.priority }}\nRisk Level: {{ $json.riskLevel }}\nComplexity: {{ $json.complexity }}\nTags: {{ $json.tags }}\n\nAnalyze this task and determine:\n1. Is this task actionable by an AI agent? (yes/no)\n2. What type of work is required? (code, documentation, testing, refactoring, etc.)\n3. What files might need to be modified?\n4. What are the prerequisites?\n5. Confidence level for autonomous completion (0.0-1.0) - BE CONSERVATIVE\n6. Estimated time to complete (in hours)\n7. Any dependencies on other tasks?\n\nProvide response as valid JSON only:\n{\n  \"actionable\": false,\n  \"workType\": \"documentation\",\n  \"affectedFiles\": [\"list of file paths\"],\n  \"prerequisites\": [\"list of requirements\"],\n  \"confidence\": 0.05,\n  \"estimatedHours\": 1,\n  \"dependencies\": [\"list of dependencies\"],\n  \"reasoning\": \"detailed explanation of analysis and why confidence is low\"\n}\n\nDefault to actionable: false and confidence: 0.05 unless absolutely certain."}, {"name": "options", "value": {"temperature": 0.1, "max_tokens": 2000, "top_p": 0.9, "stop": ["```", "---"]}}, {"name": "stream", "value": false}]}, "options": {"timeout": 30000}}, "id": "task-analysis", "name": "Analyze Task Feasibility (Gemma 3)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.actionable }}", "value2": true}]}}, "id": "actionable-check", "name": "Is Task Actionable?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.priority }}", "value2": "high"}], "number": [{"value1": "={{ $json.confidence }}", "operation": "largerEqual", "value2": 0.3}]}, "combineOperation": "any"}, "id": "priority-check", "name": "High Priority or Confident?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [900, 200]}, {"parameters": {"url": "http://supervisor-ui:3001/webhook/request-approval", "sendBody": true, "bodyParameters": {"parameters": [{"name": "agentId", "value": "task-discovery-agent"}, {"name": "action", "value": "={{ $json.task }}"}, {"name": "description", "value": "=Task discovered in {{ $json.filePath }}:\\n{{ $json.task }}\\n\\nWork Type: {{ $json.workType }}\\nEstimated Hours: {{ $json.estimatedHours }}\\nReasoning: {{ $json.reasoning }}"}, {"name": "confidence", "value": "={{ $json.confidence }}"}, {"name": "riskLevel", "value": "={{ $json.riskLevel }}"}, {"name": "affectedFiles", "value": "={{ $json.affectedFiles }}"}, {"name": "estimatedImpact", "value": "={{ $json.estimatedHours }} hours, {{ $json.workType }} work"}]}}, "id": "request-approval", "name": "Request Human Approval", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1120, 100]}, {"parameters": {"url": "http://n8n:5678/webhook/coding-request", "sendBody": true, "bodyParameters": {"parameters": [{"name": "task", "value": "={{ $json.task }}"}, {"name": "description", "value": "=Auto-assigned task from {{ $json.filePath }}:\\n{{ $json.task }}\\n\\nWork Type: {{ $json.workType }}\\nEstimated Hours: {{ $json.estimatedHours }}"}, {"name": "files", "value": "={{ $json.affectedFiles }}"}, {"name": "priority", "value": "={{ $json.priority }}"}, {"name": "source", "value": "markdown-discovery"}, {"name": "taskId", "value": "={{ $json.taskId }}"}]}}, "id": "auto-assign", "name": "Auto-Assign to Coding Agent", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"url": "http://markdown-analyzer:3003/api/tasks/{{ $json.taskId }}/complete", "sendBody": true, "bodyParameters": {"parameters": [{"name": "agentId", "value": "autonomous-coder-1"}]}}, "id": "mark-completed", "name": "<PERSON> Completed", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"jsCode": "// Store task in queue for later processing\nconst task = {\n  id: $json.taskId,\n  task: $json.task,\n  filePath: $json.filePath,\n  priority: $json.priority,\n  riskLevel: $json.riskLevel,\n  complexity: $json.complexity,\n  tags: $json.tags,\n  reason: 'Not actionable by AI agent',\n  analysis: $json.reasoning || 'Task requires human intervention',\n  queuedAt: new Date().toISOString()\n};\n\nconsole.log('Task queued for human review:', task.task);\n\nreturn { \n  success: true, \n  action: 'queued',\n  task: task\n};"}, "id": "queue-task", "name": "Queue for Human Review", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"url": "http://supervisor-ui:3001/webhook/agent-status", "sendBody": true, "bodyParameters": {"parameters": [{"name": "agentId", "value": "task-discovery-agent"}, {"name": "status", "value": "active"}, {"name": "currentTask", "value": "=Processing: {{ $json.task.substring(0, 50) }}..."}, {"name": "progress", "value": 50}, {"name": "lastActivity", "value": "={{ new Date().toISOString() }}"}]}}, "id": "update-status", "name": "Update Agent Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [460, 500]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"taskId\": $json.taskId,\n  \"action\": $json.action || \"processed\",\n  \"actionable\": $json.actionable,\n  \"confidence\": $json.confidence,\n  \"workType\": $json.workType\n} }}"}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 300]}], "connections": {"New Task Discovered": {"main": [[{"node": "Update Agent Status", "type": "main", "index": 0}, {"node": "Analyze Task Feasibility", "type": "main", "index": 0}]]}, "Analyze Task Feasibility": {"main": [[{"node": "Is Task Actionable?", "type": "main", "index": 0}]]}, "Is Task Actionable?": {"main": [[{"node": "High Priority or Confident?", "type": "main", "index": 0}], [{"node": "Queue for Human Review", "type": "main", "index": 0}]]}, "High Priority or Confident?": {"main": [[{"node": "Request Human Approval", "type": "main", "index": 0}], [{"node": "Auto-Assign to Coding Agent", "type": "main", "index": 0}]]}, "Auto-Assign to Coding Agent": {"main": [[{"node": "<PERSON> Completed", "type": "main", "index": 0}]]}, "Mark Task Completed": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Queue for Human Review": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Request Human Approval": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-01T00:00:00.000Z", "updatedAt": "2025-01-01T00:00:00.000Z", "id": "task-discovery", "name": "task-discovery"}], "triggerCount": 1, "updatedAt": "2025-01-01T00:00:00.000Z", "versionId": "1"}