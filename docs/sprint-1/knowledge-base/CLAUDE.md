```markdown
# 1. Documentation

## 1.1 Task Master AI - Claude Code Integration Guide

Tech stack:
- Node.js
- Typescript

### 1.1.1 Project Setup
- [ ] task-master init - Initialize Task Master in current project (M1)
- [ ] task-master parse-prd .taskmaster/docs/prd.txt - Generate tasks from PRD document (M1)
- [ ] task-master models --setup - Configure AI models interactively (M1)

### 1.1.2 Daily Development Workflow
- [ ] task-master list - Show all tasks with status (M1)
- [ ] task-master next - Get next available task to work on (M1)
- [ ] task-master show <id> - View detailed task information (e.g., task-master show 1.2) (M1)
- [ ] task-master set-status --id=<id> --status=done - Mark task complete (M1)

### 1.1.3 Task Management
- [ ] task-master add-task --prompt="description" --research - Add new task with AI assistance (M1)
- [ ] task-master expand --id=<id> --research --force - Break task into subtasks (M1)
- [ ] task-master update-task --id=<id> --prompt="changes" - Update specific task (M1)
- [ ] task-master update --from=<id> --prompt="changes" - Update multiple tasks from ID onwards (M1)
- [ ] task-master update-subtask --id=<id> --prompt="notes" - Add implementation notes to subtask (M1)

### 1.1.4 Analysis & Planning
- [ ] task-master analyze-complexity --research - Analyze task complexity (M1)
- [ ] task-master complexity-report - View complexity analysis (M1)
- [ ] task-master expand --all --research - Expand all eligible tasks (M1)

### 1.1.5 Dependencies & Organization
- [ ] task-master add-dependency --id=<id> --depends-on=<id> - Add task dependency (M1)
- [ ] task-master move --from=<id> --to=<id> - Reorganize task hierarchy (M1)
- [ ] task-master validate-dependencies - Check for dependency issues (M1)
- [ ] task-master generate - Update task markdown files (usually auto-called) (M1)

### 1.1.6 Key Files & Project Structure
- [ ] `.taskmaster/tasks/... - Contains task definitions. (M1)
```