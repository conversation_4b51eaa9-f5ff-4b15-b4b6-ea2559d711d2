```markdown
# 1. Database Sidecars Roadmap

Fully use Clean Architecture and DDD

avoid use prisma in domain layer

Tech stack:
- Typescript
- Node.js
- PostgreSQL
- Docker
- Tilt
- Shell Scripts

## 1.1 TaaS
Tenant as a Service

- [ ] 1.1.1. Create Tenant (or user pool) - M1
- [ ] 1.1.2. Delete Tenant (or user pool) - M2
- [ ] 1.1.3. Get Tenant (or user pool) - M3
- [ ] 1.1.4. Update Tenant (or user pool) - M4
- [ ] 1.1.5. Get Tenants (or user pools) - M5
- [ ] 1.1.6. Update Tenants (or user pools) - M6
- [ ] 1.1.7. Delete Tenants (or user pools) - M7
- [ ] 1.1.8. Tenant is a User Pool - M8
- [ ] 1.1.9. Tenant have a list of roles - M9
- [ ] 1.1.10. Role is a User Group - M10
- [ ] 1.1.11. Role have a list of policies - M11
- [ ] 1.1.12. Policy is a User Policy - M12
- [ ] 1.1.13. Policy have a list of actions - M13
- [ ] 1.1.14. Action is a User Action - M14
- [ ] 1.1.15. Action have a list of resources - M15
- [ ] 1.1.16. Resource is a User Resourc...

## 1.2 Database Sidecar Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │  Database       │    │   Backend       │
│   Container     │◄───┤  Migrator       │◄───┤   Service       │
│                 │    │  Sidecar        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 1.3 Database Sidecar Components

### 1. Database Initialization
- **File**: `services/orchestration/postgres-init/02-create-databases.sql`
- **Purpose**: Creates all required databases for microservices
- **Databases Created**:
  - `beauty_crm_appointment`
  - `beauty_crm_treatment`
  - `beauty_crm_salon`
  - `beauty_crm_staff`
  - `beauty_crm_identity`

### 2. Migration Sidecars
- **File**: `services/orchestration/docker-compose.db-sidecars.yml`
- **Purpose**: Defines migration containers for each service
- **Services**:
  - `appointment-db-migrator`
  - `treatment-db-migrator`
  - `salon-db-migrator`
  - `staff-db-migrator`
  - `identity-db-migrator`

### 3. Migrator Dockerfiles
- **Template**: `services/*/Dockerfile.migrator`
- **Purpose**: Lightweight containers that run Prisma migrations
- **Features**:
  - Wait for PostgreSQL to be ready
  - Run Prisma migrations (`prisma migrate deploy`)
  - Generate Prisma client
  - Run database seeds (if available)
  - One-time execution (restart: "no")

## 1.4 Database Sidecar Usage

### 1.4.1 Setup
```bash
# Setup migrator Dockerfiles for all services
./scripts/setup-db-migrators.sh
```

### 1.4.2 Start with Tilt
```bash
# Start all services including database sidecars
tilt up

# Start only database services
tilt up --only databases
```

### 1.4.3 Manual Migration
```bash
# Run migration for specific service
docker run --rm \
  -e DATA...
```

## 1.5 Database Sidecar Considerations

- **Data Consistency**: Ensure database migrations are atomic and consistent.
- **Rollback Strategy**: Implement a rollback strategy for failed migrations.
- **Environment Variables**: Securely manage database credentials using environment variables.
- **Monitoring**: Monitor database migration processes for errors and performance.
```