#!/bin/bash

# Beauty CRM Autonomous AI Agents Startup Script
# ⚠️ EXTREME CAUTION: This manages 2500+ files with AI agents

set -e

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_ROOT="/private/var/www/2025/ollamar1/beauty-crm"
CONFIDENCE_THRESHOLD=0.1

echo -e "${RED}🚨 AUTONOMOUS AI CODING AGENTS STARTUP 🚨${NC}"
echo -e "${RED}⚠️  EXTREME CAUTION REQUIRED - MANAGING 2500+ FILES ⚠️${NC}"
echo ""

# Safety checks
echo -e "${YELLOW}Performing safety checks...${NC}"

# Check if we're in the right directory
if [[ ! -f "$SCRIPT_DIR/docker-compose.yml" ]]; then
    echo -e "${RED}Error: docker-compose.yml not found. Run from autonomous-ai-agents directory.${NC}"
    exit 1
fi

# Check if workspace exists
if [[ ! -d "$WORKSPACE_ROOT" ]]; then
    echo -e "${RED}Error: Workspace not found at $WORKSPACE_ROOT${NC}"
    exit 1
fi

# Check for required environment variables
if [[ -z "$GEMINI_API_KEY" ]]; then
    echo -e "${RED}Error: GEMINI_API_KEY environment variable not set${NC}"
    echo "Please set your Gemini 2.5 API key:"
    echo "export GEMINI_API_KEY='your_api_key_here'"
    exit 1
fi

# Check if .env file exists
if [[ ! -f "$SCRIPT_DIR/.env" ]]; then
    echo -e "${YELLOW}Creating .env file from template...${NC}"
    cp "$SCRIPT_DIR/.env.example" "$SCRIPT_DIR/.env"
    echo -e "${RED}Please edit .env file with your API keys before continuing!${NC}"
    exit 1
fi

# Git safety check
cd "$WORKSPACE_ROOT"
if [[ -n "$(git status --porcelain)" ]]; then
    echo -e "${YELLOW}Warning: Workspace has uncommitted changes.${NC}"
    echo "Current git status:"
    git status --short
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Aborting. Please commit or stash changes first."
        exit 1
    fi
fi

# Create backup branch
echo -e "${BLUE}Creating backup branch...${NC}"
BACKUP_BRANCH="backup-before-ai-agents-$(date +%Y%m%d-%H%M%S)"
git checkout -b "$BACKUP_BRANCH"
git checkout main
echo -e "${GREEN}Backup branch created: $BACKUP_BRANCH${NC}"

# Return to script directory
cd "$SCRIPT_DIR"

# Final confirmation
echo ""
echo -e "${RED}🚨 FINAL WARNING 🚨${NC}"
echo -e "${RED}You are about to start autonomous AI agents that will:${NC}"
echo -e "${RED}- Analyze and potentially modify 2500+ files${NC}"
echo -e "${RED}- Make code changes with confidence threshold: $CONFIDENCE_THRESHOLD${NC}"
echo -e "${RED}- Require human approval for most actions${NC}"
echo -e "${RED}- Create git branches and commits automatically${NC}"
echo ""
echo -e "${YELLOW}Safety measures in place:${NC}"
echo -e "${GREEN}✓ Backup branch created: $BACKUP_BRANCH${NC}"
echo -e "${GREEN}✓ Human supervision interface enabled${NC}"
echo -e "${GREEN}✓ Quality gate validation active${NC}"
echo -e "${GREEN}✓ Emergency stop procedures available${NC}"
echo -e "${GREEN}✓ Low confidence threshold ($CONFIDENCE_THRESHOLD) requires approval${NC}"
echo ""

read -p "Are you absolutely sure you want to proceed? (type 'YES' to continue): " -r
if [[ $REPLY != "YES" ]]; then
    echo "Aborting startup."
    exit 1
fi

echo ""
echo -e "${BLUE}Starting AI agent system...${NC}"

# Start Docker services
echo -e "${BLUE}Starting Docker containers...${NC}"
docker-compose up -d

# Wait for services to be ready
echo -e "${BLUE}Waiting for services to start...${NC}"
sleep 10

# Health checks
echo -e "${BLUE}Performing health checks...${NC}"

# Check n8n
if curl -s http://localhost:5678/health > /dev/null; then
    echo -e "${GREEN}✓ n8n is running${NC}"
else
    echo -e "${RED}✗ n8n health check failed${NC}"
fi

# Check supervisor
if curl -s http://localhost:3001/health > /dev/null; then
    echo -e "${GREEN}✓ Supervisor UI is running${NC}"
else
    echo -e "${RED}✗ Supervisor UI health check failed${NC}"
fi

# Check quality gate
if curl -s http://localhost:3002/health > /dev/null; then
    echo -e "${GREEN}✓ Quality Gate is running${NC}"
else
    echo -e "${RED}✗ Quality Gate health check failed${NC}"
fi

echo ""
echo -e "${GREEN}🚀 AI Agent System Started Successfully!${NC}"
echo ""
echo -e "${BLUE}Access Points:${NC}"
echo -e "${GREEN}• Supervisor Dashboard: http://supervisor.beauty-crm.localhost${NC}"
echo -e "${GREEN}• n8n Workflow Designer: http://n8n.beauty-crm.localhost${NC}"
echo -e "${GREEN}• Quality Gate API: http://localhost:3002${NC}"
echo ""
echo -e "${YELLOW}Next Steps:${NC}"
echo "1. Open Supervisor Dashboard to monitor agent activities"
echo "2. Import workflow from workflows/autonomous-coding-workflow.json into n8n"
echo "3. Configure AI model connections in n8n"
echo "4. Test with a simple, low-risk task first"
echo ""
echo -e "${RED}Emergency Procedures:${NC}"
echo -e "${RED}• Emergency Stop: curl -X POST http://localhost:3001/api/emergency-stop${NC}"
echo -e "${RED}• View Logs: docker-compose logs [service-name]${NC}"
echo -e "${RED}• Restore Backup: git checkout $BACKUP_BRANCH${NC}"
echo ""
echo -e "${YELLOW}Remember: Always supervise AI agents and be ready to intervene!${NC}"

# Monitor startup
echo -e "${BLUE}Monitoring system startup (Ctrl+C to exit monitoring)...${NC}"
echo "Press Ctrl+C to exit monitoring and continue with manual supervision."

# Show live logs for a few seconds
timeout 30 docker-compose logs -f --tail=10 || true

echo ""
echo -e "${GREEN}System is running. Monitor via Supervisor Dashboard.${NC}"
echo -e "${YELLOW}Stay vigilant and maintain human oversight at all times!${NC}"
